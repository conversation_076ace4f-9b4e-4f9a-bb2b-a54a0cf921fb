#include "communication_adapter_base.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "comm_adapter_base";

// ========== 通用接口实现 ==========

std::string CommunicationAdapterBase::get_last_error() const {
  return last_error_message_;
}

void CommunicationAdapterBase::reset_error_state() {
  last_error_ = CommunicationError::SUCCESS;
  last_error_message_.clear();
}

CommunicationError CommunicationAdapterBase::get_last_error_code() const {
  return last_error_;
}

size_t CommunicationAdapterBase::get_success_count() const {
  return stats_.success_count;
}

size_t CommunicationAdapterBase::get_error_count() const {
  return stats_.error_count;
}

CommunicationStats CommunicationAdapterBase::get_statistics() const {
  return stats_;
}

void CommunicationAdapterBase::reset_statistics() {
  stats_ = CommunicationStats{};
}

// ========== 保护的通用方法实现 ==========

void CommunicationAdapterBase::set_error(CommunicationError error, const std::string& message) {
  last_error_ = error;
  last_error_message_ = message;
  stats_.last_error = error;
  stats_.last_error_timestamp = esphome::millis();
  
  ESP_LOGW(TAG, "通信错误 [%s]: %s", get_adapter_type().c_str(), message.c_str());
}

void CommunicationAdapterBase::update_statistics(bool success, CommunicationError error) {
  if (success) {
    stats_.success_count++;
  } else {
    stats_.error_count++;
    
    // 根据错误类型更新特定计数器
    switch (error) {
      case CommunicationError::TIMEOUT:
        stats_.timeout_count++;
        break;
      case CommunicationError::CHECKSUM_ERROR:
        stats_.checksum_error_count++;
        break;
      case CommunicationError::HARDWARE_ERROR:
        stats_.hardware_error_count++;
        break;
      case CommunicationError::DEVICE_NOT_AVAILABLE:
        stats_.device_not_available_count++;
        break;
      case CommunicationError::INVALID_RESPONSE:
        stats_.invalid_response_count++;
        break;
      default:
        break;
    }
  }
}

bool CommunicationAdapterBase::check_initialized() const {
  if (!initialized_) {
    ESP_LOGW(TAG, "%s适配器未初始化", get_adapter_type().c_str());
    return false;
  }
  return true;
}

bool CommunicationAdapterBase::verify_register_value(int16_t expected_value, int32_t actual_value, uint8_t register_address) {
  if (actual_value == expected_value) {
    ESP_LOGI(TAG, "%s寄存器 0x%02X 写入验证成功，值: %d", 
             get_adapter_type().c_str(), register_address, expected_value);
    return true;
  } else {
    std::string error_msg = get_adapter_type() + "寄存器写入验证失败，写入值=" + 
                            std::to_string(expected_value) + "，读回值=" + std::to_string(actual_value);
    set_error(CommunicationError::HARDWARE_ERROR, error_msg);
    return false;
  }
}

void CommunicationAdapterBase::log_operation_start(const std::string& operation_name, uint8_t register_address, int32_t value) {
  if (value != 0) {
    ESP_LOGI(TAG, "开始%s %s寄存器 0x%02X，值: %d", 
             operation_name.c_str(), get_adapter_type().c_str(), register_address, value);
  } else {
    ESP_LOGI(TAG, "开始%s %s寄存器 0x%02X", 
             operation_name.c_str(), get_adapter_type().c_str(), register_address);
  }
}

void CommunicationAdapterBase::log_operation_result(const std::string& operation_name, uint8_t register_address, bool success, int32_t value) {
  if (success) {
    if (value != 0) {
      ESP_LOGI(TAG, "%s %s寄存器 0x%02X 成功，值: %d", 
               operation_name.c_str(), get_adapter_type().c_str(), register_address, value);
    } else {
      ESP_LOGI(TAG, "%s %s寄存器 0x%02X 成功", 
               operation_name.c_str(), get_adapter_type().c_str(), register_address);
    }
  } else {
    ESP_LOGE(TAG, "%s %s寄存器 0x%02X 失败: %s", 
             operation_name.c_str(), get_adapter_type().c_str(), register_address, last_error_message_.c_str());
  }
}

} // namespace bl0906_factory
} // namespace esphome 