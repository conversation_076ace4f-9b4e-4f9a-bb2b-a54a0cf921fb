"""BL0906 Factory - 发布版电能计量组件"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import uart, i2c, spi, sensor
from esphome import pins
from esphome.const import (
    CONF_ID,
    CONF_UPDATE_INTERVAL
)

# 发布版依赖管理
DEPENDENCIES = []
CODEOWNERS = ["@carrot8848"]
AUTO_LOAD = ["sensor"]  # 发布版不包含number组件
MULTI_CONF = True

# 发布版类声明 - 使用薄包装层
bl0906_factory_ns = cg.esphome_ns.namespace("bl0906_factory")
BL0906Wrapper = bl0906_factory_ns.class_("BL0906Wrapper", cg.PollingComponent)

# 通信适配器类声明（发布版仍需要通信功能）
CommunicationAdapterInterface = bl0906_factory_ns.class_("CommunicationAdapterInterface")
UartCommunicationAdapter = bl0906_factory_ns.class_("UartCommunicationAdapter", CommunicationAdapterInterface, uart.UARTDevice)
SpiCommunicationAdapter = bl0906_factory_ns.class_("SpiCommunicationAdapter", CommunicationAdapterInterface, spi.SPIDevice)

# 发布版枚举定义（只保留必要的）
SensorType = bl0906_factory_ns.enum("SensorType", is_class=True)
EEPROMType = bl0906_factory_ns.enum("EEPROMType", is_class=True)
FreqAdaptMode = bl0906_factory_ns.enum("FreqAdaptMode", is_class=True)

# 导入简化的配置映射
from .config_mappings import (
    COMMUNICATION_MODES,
    STORAGE_TYPES,
    EEPROM_TYPES,
    FREQ_ADAPT_MODES,
    VOLTAGE_SAMPLING_MODES,
    CHIP_MODELS,
    ConfigValidator,
)

# 发布版常量定义（简化）
CONF_BL0906_FACTORY_ID = "bl0906_factory_id"
CONF_CHIP_MODEL = "chip_model"
CONF_COMMUNICATION = "communication"
CONF_STORAGE_TYPE = "storage_type"
CONF_EEPROM_TYPE = "eeprom_type"
CONF_INSTANCE_ID = "instance_id"
CONF_UART_ID = "uart_id"
CONF_SPI_ID = "spi_id"
CONF_CS_PIN = "cs_pin"
CONF_FREQ_ADAPT = "freq_adapt"
CONF_VOLTAGE_SAMPLING_MODE = "voltage_sampling_mode"
CONF_ENERGY_STATISTICS = "energy_statistics"
CONF_TIME_ID = "time_id"

def validate_chip_configuration(config):
    """验证芯片配置的合理性（发布版简化）"""
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    if chip_model not in CHIP_MODELS:
        raise cv.Invalid(f"Unsupported chip model: {chip_model}")
    return config

def validate_energy_statistics_config(config):
    """验证电量统计配置的完整性"""
    energy_statistics_enabled = config.get(CONF_ENERGY_STATISTICS, False)
    time_id_configured = CONF_TIME_ID in config
    
    if energy_statistics_enabled and not time_id_configured:
        raise cv.Invalid(
            f"When '{CONF_ENERGY_STATISTICS}' is set to true, "
            f"'{CONF_TIME_ID}' must be configured for energy tracking."
        )
    
    return config

# 发布版配置模式（精简版）
BASE_CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Wrapper),
    cv.Optional(CONF_CHIP_MODEL, default="bl0906"): cv.enum(CHIP_MODELS, lower=True),
    cv.Required(CONF_COMMUNICATION): cv.enum(COMMUNICATION_MODES, lower=True),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Required(CONF_INSTANCE_ID): cv.hex_uint32_t,
    cv.Optional(CONF_FREQ_ADAPT, default="off"): cv.enum(FREQ_ADAPT_MODES, lower=True),
    cv.Optional(CONF_VOLTAGE_SAMPLING_MODE, default="transformer"): cv.enum(VOLTAGE_SAMPLING_MODES, lower=True),
    # 电量统计功能配置
    cv.Optional(CONF_ENERGY_STATISTICS, default=False): cv.boolean,
    cv.Optional(CONF_TIME_ID): cv.use_id("time"),
    # UART模式配置
    cv.Optional(CONF_UART_ID): cv.use_id(uart.UARTComponent),
    # SPI模式配置
    cv.Optional(CONF_SPI_ID): cv.use_id(spi.SPIComponent),
    cv.Optional(CONF_CS_PIN): pins.gpio_output_pin_schema,
    # 简化的存储配置（发布版只读）
    cv.Optional(CONF_STORAGE_TYPE, default="preferences"): cv.enum(STORAGE_TYPES, lower=True),
    cv.Optional("i2c_id"): cv.use_id(i2c.I2CBus),
    cv.Optional("address"): cv.i2c_address,
    cv.Optional(CONF_EEPROM_TYPE, default="24c02"): cv.enum(EEPROM_TYPES, lower=True),
}).extend(cv.polling_component_schema("60s"))

CONFIG_SCHEMA = cv.All(
    BASE_CONFIG_SCHEMA,
    ConfigValidator.validate_communication_config,
    ConfigValidator.validate_i2c_dependency,
    validate_chip_configuration,
    validate_energy_statistics_config
)

FINAL_VALIDATE_SCHEMA = ConfigValidator.validate_i2c_dependency

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    
    # 添加预编译库的源文件（临时使用真实实现）
    cg.add_library("bl0906_core_impl", None)  # 预编译库链接

    # 发布版标识
    cg.add_define("USE_BL0906_FACTORY")
    cg.add_define("BL0906_PRODUCTION_BUILD")
    cg.add_define("BL0906_READONLY_CALIBRATION")
    cg.add_define("BL0906_FAULT_TOLERANT")
    
    # 设置运行时芯片型号
    chip_model = config.get(CONF_CHIP_MODEL, "bl0906")
    chip_enum_map = {
        "bl0906": "esphome::bl0906_factory::ChipModel::BL0906",
        "bl0910": "esphome::bl0906_factory::ChipModel::BL0910"
    }
    cg.add(var.set_chip_model(cg.RawExpression(chip_enum_map[chip_model])))

    # 通信适配器配置
    comm_mode = config[CONF_COMMUNICATION]
    if comm_mode == "spi":
        cg.add_build_flag("-DUSE_SPI_COMMUNICATION_ADAPTER")
        cg.add_library("SPI", None)
        
        spi_component = await cg.get_variable(config[CONF_SPI_ID])
        cs_pin = await cg.gpio_pin_expression(config[CONF_CS_PIN])
        
        cg.add(cg.RawExpression(f"""
        #ifdef USE_SPI_COMMUNICATION_ADAPTER
        {{
            auto spi_adapter_impl = new esphome::bl0906_factory::SpiCommunicationAdapter();
            spi_adapter_impl->set_spi_parent({spi_component});
            spi_adapter_impl->set_cs_pin({cs_pin});
            auto spi_adapter = std::unique_ptr<esphome::bl0906_factory::CommunicationAdapterInterface>(spi_adapter_impl);
            {var}->set_communication_adapter(std::move(spi_adapter));
        }}
        #endif
        """))
        
    elif comm_mode == "uart":
        cg.add_build_flag("-DUSE_UART_COMMUNICATION_ADAPTER")
        
        uart_component = await cg.get_variable(config[CONF_UART_ID])
        
        cg.add(cg.RawExpression(f"""
        #ifdef USE_UART_COMMUNICATION_ADAPTER
        {{
            auto uart_adapter_impl = new esphome::bl0906_factory::UartCommunicationAdapter({uart_component});
            auto uart_adapter = std::unique_ptr<esphome::bl0906_factory::CommunicationAdapterInterface>(uart_adapter_impl);
            {var}->set_communication_adapter(std::move(uart_adapter));
        }}
        #endif
        """))

    # 发布版存储配置（只读模式）
    storage_type = config.get(CONF_STORAGE_TYPE, "preferences")
    cg.add(var.set_storage_type(storage_type))
    
    if storage_type == "eeprom":
        cg.add_build_flag("-DUSE_I2C_EEPROM_CALIBRATION")
        
        if "i2c_id" in config and "address" in config:
            i2c_component = await cg.get_variable(config["i2c_id"])
            
            cg.add(cg.RawExpression(f"""
            #ifdef USE_I2C_EEPROM_CALIBRATION
            {var}->set_i2c_parent({i2c_component});
            {var}->set_i2c_address({config["address"]});
            #endif
            """))

            eeprom_type = config.get(CONF_EEPROM_TYPE, "24c02")
            eeprom_enum_map = {
                "24c02": "esphome::bl0906_factory::EEPROMType::TYPE_24C02",
                "24c04": "esphome::bl0906_factory::EEPROMType::TYPE_24C04",
                "24c08": "esphome::bl0906_factory::EEPROMType::TYPE_24C08",
                "24c16": "esphome::bl0906_factory::EEPROMType::TYPE_24C16"
            }

            cg.add(cg.RawExpression(f"""
            #ifdef USE_I2C_EEPROM_CALIBRATION
            {var}->set_eeprom_type({eeprom_enum_map[eeprom_type]});
            #endif
            """))

            cg.add_library("Wire", None)
        else:
            raise ValueError("EEPROM storage requires i2c_id and address configuration")
    
    # 设置实例ID
    instance_id = config[CONF_INSTANCE_ID]
    cg.add(var.set_instance_id(instance_id))
    
    # 频率适配模式配置
    freq_adapt_mode = config[CONF_FREQ_ADAPT]
    freq_adapt_enum_map = {
        "off": "esphome::bl0906_factory::BL0906Factory::FreqAdaptMode::OFF",
        "auto": "esphome::bl0906_factory::BL0906Factory::FreqAdaptMode::AUTO",
        "60": "esphome::bl0906_factory::BL0906Factory::FreqAdaptMode::HZ60"
    }
    cg.add(var.set_freq_adapt_mode(cg.RawExpression(freq_adapt_enum_map[freq_adapt_mode])))
    
    # 电压采样模式配置
    voltage_sampling_mode = config[CONF_VOLTAGE_SAMPLING_MODE]
    if voltage_sampling_mode == "transformer":
        cg.add_build_flag("-DBL0906_VOLTAGE_SAMPLING_TRANSFORMER")
    elif voltage_sampling_mode == "resistor_divider":
        cg.add_build_flag("-DBL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER")

    # 电量统计功能配置
    energy_statistics_enabled = config.get(CONF_ENERGY_STATISTICS, False)
    cg.add(var.set_energy_statistics_enabled(energy_statistics_enabled))
    
    if CONF_TIME_ID in config:
        time_component = await cg.get_variable(config[CONF_TIME_ID])
        cg.add(var.set_time_component(time_component))

# 发布版平台模式定义
PLATFORM_SCHEMA = cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})
