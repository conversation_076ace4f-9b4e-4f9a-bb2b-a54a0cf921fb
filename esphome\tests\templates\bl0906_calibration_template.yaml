# BL0906校准参数模板
# 使用方法:
# packages:
#   calib_ch1: !include
#     file: templates/bl0906_calibration_template.yaml
#     vars:
#       channel_num: 1
#       channel_name: "CH_1"

defaults:
  channel_num: "1"
  channel_name: "CH_1"

number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    
    chgn_decimal_${channel_num}:
      name: "${channel_name} Current Gain"
      id: chgn_${channel_num}_adjust
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_${channel_num}:
      name: "${channel_name} Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_${channel_num}:
      name: "${channel_name} RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_${channel_num}:
      name: "${channel_name} RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current 