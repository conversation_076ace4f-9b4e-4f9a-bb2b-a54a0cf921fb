#pragma once
#include <vector>
#include <map>
#include <cstdint>
#include <string>

namespace esphome {
namespace bl0906_factory {

// 校准条目结构
struct CalibrationEntry {
    uint8_t register_addr;
    int16_t value;
} __attribute__((packed));

// 统一的存储接口
class CalibrationStorageInterface {
public:
    virtual ~CalibrationStorageInterface() = default;
    
    virtual bool init() = 0;
    virtual bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) = 0;
    virtual bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) = 0;
    virtual bool delete_instance(uint32_t instance_id) = 0;
    virtual bool verify() = 0;
    virtual bool erase() = 0;
    
    virtual std::vector<uint32_t> get_instance_list() = 0;
    virtual size_t get_max_instances() = 0;
    virtual std::string get_storage_type() const = 0;
    
    // 用于类型检查的虚函数（替代dynamic_cast）
    virtual bool is_eeprom_storage() const { return false; }
};

}  // namespace bl0906_factory
}  // namespace esphome 