# BL0906调试按钮模板
# 使用方法:
# packages:
#   debug_buttons: !include
#     file: templates/bl0906_debug_buttons.yaml
#     vars:
#       bl0906_id: sensor_bl0906

defaults:
  bl0906_id: sensor_bl0906

button:
  # 基础操作按钮
  # 电量控制按钮
  - platform: template
    name: "Reset Total Energy"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");

  # 校准相关按钮
  - platform: template
    name: "CALCULATE RMSOS"
    on_press:
      - lambda: |-
          auto bl0906 = id(${bl0906_id});
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

  - platform: template
    name: "Save Calibration Data to Flash"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
        
  - platform: template  
    name: "Read Calibration Data From Chip"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          bl0906->refresh_all_calib_numbers();
        }
        
  - platform: template
    name: "Read Calibration Data from Flash"
    icon: "mdi:database-search"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            if (bl0906 != nullptr) {
              bl0906->read_and_display_calibration_data();
            }

  - platform: template
    name: "Show All Instance Calibration Data"
    icon: "mdi:database-outline"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            if (bl0906 != nullptr) {
              bl0906->show_all_instances_calibration_data();
            }

  - platform: template
    name: "Clear Storage (Fix Full Storage)"
    icon: "mdi:delete-sweep"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            if (bl0906 != nullptr) {
              bl0906->clear_calibration_storage();
            }

  - platform: template
    name: "Show Storage Status"
    icon: "mdi:information"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(${bl0906_id}));
            if (bl0906 != nullptr) {
              bl0906->show_storage_status();
            }

  # 批量操作按钮
  - platform: template
    name: "批量更新所有CHGN值"
    id: update_all_chgn_button
    on_press:
      - lambda: |-
          id(${bl0906_id})->update_all_chgn_values_from_sensor(); 
    web_server: 
      sorting_group_id: calibrate
      
  - platform: template
    name: "CHGN 0"
    on_press:
      - lambda: id(${bl0906_id})->reset_all_chgn_values_to_zero();
    web_server: 
      sorting_group_id: calibrate
      
  - platform: template
    name: "RMSOS 0"
    on_press:
      - lambda: id(${bl0906_id})->reset_all_rmsos_values_to_zero();
    web_server: 
      sorting_group_id: calibrate 