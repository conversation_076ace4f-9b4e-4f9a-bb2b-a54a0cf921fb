# BL0910CA组件移植开发计划

## 1. 项目概述

基于成熟的BL0906Factory组件，移植开发支持BL0910芯片的BL0910CA组件。BL0910相比BL0906最大的差异是支持10个电流通道(vs 6个)，并且寄存器地址映射有所不同。

### 1.1 目标特性
- 支持10通道电流监测 + 1通道电压监测
- 继承BL0906Factory的所有高级功能：
  - 双通信模式支持（UART/SPI）
  - 多种校准存储方式（Preferences/I2C EEPROM）
  - 能量统计管理
  - 频率自适应
  - 现代化架构设计

### 1.2 核心差异
根据寄存器对比文档，主要差异：
- **通道数量**：10通道电流 vs 6通道电流
- **寄存器地址偏移**：多数地址向前偏移1位
- **简化移植**：暂不实现外部温度传感器和相位校正功能

## 2. 文件结构规划

### 2.1 核心文件列表
```
bl0910ca/
├── __init__.py                           # Python配置入口
├── sensor.py                            # 传感器配置
├── number.py                            # 数字组件配置
├── config_mappings.py                   # 统一配置映射
├── bl0910ca.h                          # 主组件头文件
├── bl0910ca.cpp                        # 主组件实现
├── bl0910_registers.h                  # BL0910寄存器定义
├── bl0910_calibration.h                # 校准相关定义
├── bl0910_number.h                     # 数字组件头文件
├── bl0910_number.cpp                   # 数字组件实现
├── energy_statistics_manager.h         # 能量统计管理（复用）
├── energy_statistics_manager.cpp       # 能量统计管理（复用）
└── communication/                      # 通信适配器（复用BL0906的）
    ├── communication_adapter_interface.h
    ├── communication_adapter_base.h
    ├── communication_adapter_base.cpp
    ├── uart_communication_adapter.h
    ├── uart_communication_adapter.cpp
    ├── spi_communication_adapter.h
    └── spi_communication_adapter.cpp
└── storage/                           # 存储系统（复用BL0906的）
    ├── calibration_storage_interface.h
    ├── calibration_storage_base.h
    ├── calibration_storage_base.cpp
    ├── preference_calibration_storage.h
    ├── preference_calibration_storage.cpp
    ├── i2c_eeprom_calibration_storage.h
    └── i2c_eeprom_calibration_storage.cpp
```

### 2.2 复用策略
- **完全复用**：通信适配器、存储系统、能量统计管理器
- **适配修改**：寄存器定义、主组件逻辑、Python配置
- **扩展修改**：增加4个通道支持，保持功能简洁

## 3. 寄存器地址映射修改

### 3.1 关键地址差异表

| 功能类别 | BL0906地址 | BL0910地址 | 修改说明 |
|---------|-----------|-----------|----------|
| 电流1有效值 | 0x0D | 0x0C | 前移1位 |
| 电流2有效值 | 0x0E | 0x0D | 前移1位 |
| 电流3有效值 | 0x0F | 0x0E | 前移1位 |
| 电流4有效值 | 0x10 | 0x0F | 前移1位 |
| 电流5有效值 | 0x13 | 0x10 | 地址变化 |
| 电流6有效值 | 0x14 | 0x11 | 地址变化 |
| 电流7有效值 | - | 0x12 | 新增 |
| 电流8有效值 | - | 0x13 | 新增 |
| 电流9有效值 | - | 0x14 | 新增 |
| 电流10有效值 | - | 0x15 | 新增 |
| 电压有效值 | 0x16 | 0x16 | 保持不变 |

### 3.2 功率寄存器映射

| 功能 | BL0906地址 | BL0910地址 | 说明 |
|------|-----------|-----------|------|
| 功率1 | 0x23 | 0x22 | 前移1位 |
| 功率2 | 0x24 | 0x23 | 前移1位 |
| 功率3 | 0x25 | 0x24 | 前移1位 |
| 功率4 | 0x26 | 0x25 | 前移1位 |
| 功率5 | 0x29 | 0x26 | 地址变化 |
| 功率6 | 0x2A | 0x27 | 地址变化 |
| 功率7 | - | 0x28 | 新增 |
| 功率8 | - | 0x29 | 新增 |
| 功率9 | - | 0x2A | 新增 |
| 功率10 | - | 0x2B | 新增 |
| 总功率 | 0x2C | 0x2C | 保持不变 |

### 3.3 校准寄存器处理
- **RMSGN寄存器**：地址0x6C-0x76，支持11个通道（含电压）
- **简化策略**：暂不实现相位校正和外部温度功能

## 4. 代码修改计划

### 4.1 第一阶段：基础架构搭建
1. **创建基础文件结构**
   - 复制bl0906_factory文件夹为bl0910ca
   - 重命名核心文件和类名
   - 更新命名空间：`bl0906_factory` -> `bl0910ca`

2. **修改寄存器定义** (`bl0910_registers.h`)
   - 定义10通道寄存器地址数组
   - 更新校准寄存器地址映射
   - 保持简洁的寄存器结构

3. **更新主组件头文件** (`bl0910ca.h`)
   - 修改通道常量：`CHANNEL_COUNT = 10`
   - 扩展数据结构以支持10通道
   - 保留BL0906Factory的所有高级特性

### 4.2 第二阶段：核心逻辑适配
1. **主组件实现修改** (`bl0910ca.cpp`)
   - 更新数据读取循环：6通道 -> 10通道
   - 修改寄存器地址查找逻辑
   - 优化10通道数据处理流程

2. **数据转换函数更新**
   - 扩展转换函数以支持新的寄存器地址
   - 保持与BL0906相同的转换算法
   - 确保10通道数据转换的准确性

3. **校准系统适配**
   - 扩展校准Number组件至10通道
   - 更新校准寄存器地址映射
   - 保持校准存储系统兼容性

### 4.3 第三阶段：Python配置系统
1. **配置映射更新** (`config_mappings.py`)
   - 扩展传感器配置至10通道
   - 保持与BL0906Factory相同的配置结构
   - 简化配置，专注核心功能

2. **主配置文件** (`__init__.py`)
   - 更新组件类名和命名空间
   - 扩展传感器配置模式
   - 保持向后兼容的配置接口

3. **传感器和数字组件** (`sensor.py`, `number.py`)
   - 扩展至10通道支持
   - 保持配置语法一致性
   - 简化组件配置

### 4.4 第四阶段：测试和优化
1. **功能测试**
   - 创建测试配置文件
   - 验证10通道数据读取
   - 测试校准功能
   - 验证能量统计功能

2. **性能优化**
   - 优化10通道数据读取效率
   - 调整更新间隔和状态机
   - 确保内存使用合理

## 5. 关键技术要点

### 5.1 通道扩展策略
- **数据结构**：扩展原有6通道数组到10通道
- **循环逻辑**：更新所有通道遍历代码
- **寄存器映射**：使用地址数组简化通道到地址的映射

### 5.2 向后兼容性
- 保持与BL0906Factory相同的配置语法
- 用户只需修改组件名称：`bl0906_factory` -> `bl0910ca`
- 配置选项保持一致，自动适配10通道

## 6. 测试计划

### 6.1 单元测试
- 寄存器地址映射测试
- 数据转换函数测试
- 10通道数据读取测试

### 6.2 集成测试
- 10通道数据同时读取测试
- 校准功能完整测试
- 通信适配器兼容性测试
- 能量统计功能测试

### 6.3 性能测试
- 10通道数据读取效率测试
- 内存使用情况测试
- 长期稳定性测试

## 7. 文档计划

### 7.1 用户文档
- BL0910CA组件使用指南
- 配置参数说明
- 与BL0906Factory的差异对比

### 7.2 开发文档
- 寄存器地址映射表
- 移植过程记录
- 已知问题和解决方案

## 8. 开发时间表

### 第1周：基础架构
- [ ] 创建文件结构
- [ ] 完成寄存器定义
- [ ] 更新类名和命名空间

### 第2周：核心功能
- [ ] 实现10通道数据读取
- [ ] 适配校准系统
- [ ] 添加外部温度支持

### 第3周：Python配置
- [ ] 更新配置映射
- [ ] 完成Python配置文件
- [ ] 创建测试配置

### 第4周：测试优化
- [ ] 功能测试
- [ ] 性能优化
- [ ] 文档完善

## 9. 风险评估

### 9.1 技术风险
- **低风险**：10通道扩展（架构已支持）
- **低风险**：通信和存储系统复用
- **低风险**：寄存器地址映射适配

### 9.2 兼容性风险
- **低风险**：向后兼容性保持
- **低风险**：现有功能移植

### 9.3 缓解措施
- 分阶段开发，及时验证每个功能模块
- 充分测试关键功能点
- 保持与原BL0906Factory架构的一致性

## 10. 总结

BL0910CA组件移植项目基于成熟的BL0906Factory架构，主要工作是扩展通道数量和适配寄存器地址。通过复用现有的通信、存储和能量统计系统，简化移植复杂度，专注于核心的10通道电能监测功能。

预期交付一个简洁高效、支持10通道的BL0910CA组件，为用户提供更强大的多通道电能监测能力，同时保持系统的稳定性和可维护性。 