---
description:
globs:
alwaysApply: false
---
# 代码质量标准

## 核心简洁性原则

### 禁止的代码模式
- **包装函数反模式**: 严禁创建仅仅return另一个函数的包装函数
- **无意义中间层**: 不允许为了"架构完整性"而创建没有实际功能的中间层
- **冗余抽象**: 避免过度抽象，每个抽象层都必须有明确的业务价值

### 鼓励的代码模式
- **直接调用**: 优先使用直接函数调用而不是多层封装
- **功能内联**: 将简单的工具函数直接内联到调用处
- **合并相关逻辑**: 将紧密相关的小函数合并到同一个函数中

## 具体实施指导

### 函数设计原则
```cpp
// ❌ 错误示例 - 无意义的包装函数
int get_voltage() {
    return read_voltage();
}

// ✅ 正确示例 - 直接调用
// 直接使用 read_voltage() 而不需要包装函数
```

### 类设计原则
```cpp
// ❌ 错误示例 - 无意义的继承层次
class BaseReader {
    virtual int read() = 0;
};
class VoltageReader : public BaseReader {
    int read() override { return voltage_sensor.read(); }
};

// ✅ 正确示例 - 直接使用
// 直接使用 voltage_sensor.read() 或创建有实际功能的包装
```

### Python配置简化
```python
# ❌ 错误示例 - 无意义的配置层
def get_sensor_config():
    return build_sensor_config()

# ✅ 正确示例 - 直接构建或合并逻辑
# 直接调用 build_sensor_config() 或将逻辑合并
```

## 重构指导原则

### 不保持向后兼容性
- **激进重构**: 为了代码简洁性，可以破坏向后兼容性
- **接口简化**: 删除不必要的接口方法和参数
- **配置精简**: 移除过时或冗余的配置选项
- **依赖清理**: 移除不再需要的依赖和导入

### 功能合并策略
1. **识别包装函数**: 找出只是简单转发调用的函数
2. **分析调用链**: 追踪函数调用路径，消除中间层
3. **合并小函数**: 将逻辑相关的小函数合并到调用方
4. **内联工具函数**: 将简单的工具函数直接内联

### 代码审查标准
- **每个函数都必须有独特的功能逻辑**
- **禁止存在只是转发调用的函数**
- **每个类都必须封装有意义的状态和行为**
- **配置映射必须有实际的转换或验证逻辑**

## 应用到BL0906Factory组件

### 通信适配器简化
- 如果适配器只是转发调用，考虑直接使用底层接口
- 合并相似的通信逻辑到同一个方法中
- 删除不必要的适配器基类方法

### 存储接口精简
- 移除只是转发调用的存储方法
- 合并相关的存储操作到单个方法
- 简化错误处理逻辑，避免多层包装

### 配置映射优化
- 删除不必要的配置转换函数
- 将简单的映射逻辑内联到使用处
- 合并相关的配置验证逻辑

## 质量检查清单

在每次代码修改后，检查以下项目：
- [ ] 是否存在只return另一个函数的包装函数？
- [ ] 是否有不必要的中间层抽象？
- [ ] 是否可以将小函数内联到调用处？
- [ ] 是否可以合并相关的功能到同一个方法？
- [ ] 是否删除了不再需要的接口和参数？
- [ ] 是否简化了错误处理和日志记录？

## 重构优先级
1. **高优先级**: 消除包装函数和无意义转发
2. **中优先级**: 合并相关的小函数和工具方法  
3. **低优先级**: 简化接口和参数，优化命名

通过遵循这些原则，确保代码库保持高度的简洁性和可维护性，避免不必要的复杂性和冗余。
