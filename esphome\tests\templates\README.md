# BL0906 Factory 模板化配置说明

本目录包含BL0906 Factory组件的模板化配置文件，用于减少重复配置代码。

## 模板文件说明

### 1. `bl0906_channel_template.yaml` - 通道传感器模板
用于生成单个通道的所有传感器配置，包括电流、功率、电量、累计电量和各种时间统计传感器。

**参数：**
- `channel_num`: 通道号 (1-6)
- `channel_name`: 通道显示名称，如 "${ch1}"

**使用示例：**
```yaml
packages:
  ch1_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 1
      channel_name: "${ch1}"
```

### 2. `bl0906_calibration_template.yaml` - 校准参数模板
用于生成单个通道的校准参数number组件，包括电流增益、电流偏移、RMS增益和RMS偏移。

**参数：**
- `channel_num`: 通道号 (1-6)
- `channel_name`: 通道显示名称，如 "CH_1"

**使用示例：**
```yaml
packages:
  ch1_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 1
      channel_name: "CH_1"
```

### 3. `bl0906_debug_buttons.yaml` - 调试按钮模板
包含所有调试和测试功能的按钮，包括校准数据管理、电量数据控制、批量操作等。

**参数：**
- `bl0906_id`: BL0906组件的ID，默认为 `sensor_bl0906`

**使用示例：**
```yaml
packages:
  debug_buttons: !include
    file: templates/bl0906_debug_buttons.yaml
    vars:
      bl0906_id: sensor_bl0906
```

### 4. `bl0906_global_sensors.yaml` - 全局传感器模板
包含BL0906的全局传感器配置，如电压、频率、温度、总和传感器和统计传感器。

**参数：**
- `bl0906_id`: BL0906组件的ID，默认为 `sensor_bl0906`

**使用示例：**
```yaml
packages:
  global_sensors: !include templates/bl0906_global_sensors.yaml
```

### 5. `system_monitoring.yaml` - 系统监控模板
包含系统状态监控传感器和控制开关，如运行时间、内存使用量、系统信息等。

**使用示例：**
```yaml
packages:
  system_monitoring: !include templates/system_monitoring.yaml
```

## 完整使用示例

参考 `bl0906_factory_6ch_spi_templated.yaml` 文件，展示了如何使用这些模板创建完整的6通道配置：

```yaml
# 主配置文件示例
packages:
  # 全局传感器
  global_sensors: !include templates/bl0906_global_sensors.yaml
  
  # 6个通道的传感器配置
  ch1_sensors: !include
    file: templates/bl0906_channel_template.yaml
    vars:
      channel_num: 1
      channel_name: "${ch1}"
  
  # 6个通道的校准参数配置  
  ch1_calibration: !include
    file: templates/bl0906_calibration_template.yaml
    vars:
      channel_num: 1
      channel_name: "CH_1"
      
  # 调试按钮和系统监控
  debug_buttons: !include templates/bl0906_debug_buttons.yaml
  system_monitoring: !include templates/system_monitoring.yaml
```

## 优势

1. **减少重复代码**：原本958行的配置文件现在主文件只需约120行
2. **易于维护**：修改传感器配置只需修改模板文件，所有通道自动应用
3. **可扩展性**：容易添加新通道或修改现有配置
4. **参数化**：通过变量传递，同一模板可用于不同通道
5. **模块化**：功能分离，调试功能、传感器配置、校准参数独立管理

## 3相系统专用模板

### 6. `bl0906_phase_instance_template.yaml` - 相位实例模板
用于生成单个相位的完整BL0906Factory配置，包括传感器定义和6个通道配置。

**参数：**
- `phase_name`: 相位名称，如 "A"、"B"、"C"
- `phase_lower`: 相位小写名称，如 "a"、"b"、"c"
- `instance_id`: 实例ID，如 "0x906B0001"
- `uart_id`: UART总线ID
- `ch1_name` - `ch6_name`: 各通道显示名称
- `current_group`、`power_group`、`energy_group`: Web界面分组ID

### 7. `bl0906_phase_calibration_template.yaml` - 相位校准模板
用于生成单个相位的所有校准参数number组件。

**参数：**
- `phase_name`: 相位名称
- `phase_lower`: 相位小写名称
- `current_group`、`power_group`: Web界面分组ID

### 8. `bl0906_phase_debug_buttons.yaml` - 相位调试按钮模板
用于生成单个相位的调试按钮和控制开关。

**参数：**
- `phase_name`: 相位名称
- `phase_lower`: 相位小写名称

### 9. `system_monitoring_3phase.yaml` - 3相系统监控模板
包含WiFi信息、系统信息、总功率计算等3相系统专用功能。

## 3相系统使用示例

参考 `3x6ch-monitor-factory-templated.yaml` 文件：

```yaml
packages:
  # A相完整配置
  phase_a_instance: !include
    file: templates/bl0906_phase_instance_template.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"
      instance_id: "0x906B0001"
      uart_id: "uart_bus_a"
      ch1_name: "${chA1}"
      # ... 其他通道名称
      current_group: "currentA"
      power_group: "powerA"
      energy_group: "energyA"
  
  phase_a_calibration: !include
    file: templates/bl0906_phase_calibration_template.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"
      current_group: "currentA"
      power_group: "powerA"
  
  phase_a_debug: !include
    file: templates/bl0906_phase_debug_buttons.yaml
    vars:
      phase_name: "A"
      phase_lower: "a"
  
  # B相和C相类似配置...
  
  # 系统监控
  system_monitoring: !include templates/system_monitoring_3phase.yaml
```

## 配置文件对比

| 配置类型 | 原始行数 | 模板化后行数 | 减少比例 |
|----------|----------|--------------|----------|
| 6通道SPI | 958行 | ~120行 | 87% |
| 3×6通道监控 | 1486行 | ~200行 | 86% |

## 注意事项

1. 所有模板文件必须放在 `templates/` 目录下
2. 使用 `defaults` 块为变量提供默认值
3. 变量命名使用 `${variable_name}` 格式
4. 确保传递的变量与模板中定义的变量名称一致
5. 3相系统需要正确配置UART总线和实例ID
6. 模板支持嵌套使用，可以组合不同的功能模块 