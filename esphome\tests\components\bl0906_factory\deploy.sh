#!/bin/bash
# BL0906 Factory 通用部署脚本
# 用途：根据参数选择构建发布版或生产版

VERSION_TYPE=${1:-release}

echo "=== BL0906 Factory 自动化部署 ==="
echo "版本类型: $VERSION_TYPE"
echo ""

case $VERSION_TYPE in
    "release")
        echo "构建发布版..."
        ./build_release.sh
        if [ $? -eq 0 ]; then
            echo ""
            echo "🎉 发布版构建成功！"
            echo ""
            echo "=== 发布版使用说明 ==="
            echo "发布版特点："
            echo "  ✓ 用户友好的简化配置"
            echo "  ✓ 移除校准调试功能"
            echo "  ✓ 容错运行模式"
            echo "  ✓ 核心算法保护"
            echo ""
            echo "适用场景："
            echo "  - 最终用户部署"
            echo "  - 产品发布"
            echo "  - 生产环境使用"
            echo ""
            echo "配置示例："
            echo "  bl0906_factory:"
            echo "    chip_model: bl0906"
            echo "    communication: uart"
            echo "    instance_id: 0x12345678"
            echo "    uart_id: uart_bus"
        else
            echo "❌ 发布版构建失败"
            exit 1
        fi
        ;;
    "production")
        echo "构建生产版..."
        ./build_production.sh
        if [ $? -eq 0 ]; then
            echo ""
            echo "🎉 生产版构建成功！"
            echo ""
            echo "=== 生产版使用说明 ==="
            echo "生产版特点："
            echo "  ✓ 完整的校准调试功能"
            echo "  ✓ number 组件支持"
            echo "  ✓ 初始校准值配置"
            echo "  ✓ 开发调试友好"
            echo ""
            echo "适用场景："
            echo "  - 开发调试"
            echo "  - 生产制造过程"
            echo "  - 校准测试"
            echo ""
            echo "⚠️  重要提醒："
            echo "  构建完成后请及时恢复发布版配置："
            echo "  ./restore_release.sh"
            echo ""
            echo "配置示例："
            echo "  bl0906_factory:"
            echo "    chip_model: bl0906"
            echo "    communication: uart"
            echo "    instance_id: 0x12345678"
            echo "    uart_id: uart_bus"
            echo "    calibration_mode: true"
            echo "    calibration:"
            echo "      storage_type: preferences"
            echo "      enabled: true"
        else
            echo "❌ 生产版构建失败"
            exit 1
        fi
        ;;
    "help"|"-h"|"--help")
        echo "用法: ./deploy.sh [版本类型]"
        echo ""
        echo "版本类型:"
        echo "  release     构建发布版（默认）"
        echo "  production  构建生产版"
        echo "  help        显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  ./deploy.sh              # 构建发布版"
        echo "  ./deploy.sh release      # 构建发布版"
        echo "  ./deploy.sh production   # 构建生产版"
        echo ""
        echo "版本差异:"
        echo "  发布版: 用户友好，移除校准功能，适用于最终用户"
        echo "  生产版: 完整功能，包含校准调试，适用于开发制造"
        ;;
    *)
        echo "错误：未知的版本类型 '$VERSION_TYPE'"
        echo ""
        echo "支持的版本类型："
        echo "  release     - 发布版（用户版本）"
        echo "  production  - 生产版（开发版本）"
        echo ""
        echo "使用 './deploy.sh help' 查看详细帮助"
        exit 1
        ;;
esac

echo ""
echo "=== 部署完成 ===" 