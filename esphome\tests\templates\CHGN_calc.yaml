defaults:
  chip_name: "bl0910"
  chip_id: "sensor_bl0910"
udp:
  port: 1314
    
packet_transport:
  - platform: udp
    update_interval: 1s
    providers:
       - name: develop-platform-1

sensor:
  - platform: packet_transport
    provider: develop-platform-1
    name: unit1_current
    id: unit1_current
    internal: false
    accuracy_decimals: 3
    web_server: 
      sorting_group_id: calibrate
  - platform: packet_transport
    provider: develop-platform-1
    name: unit1_voltage
    id: unit1_voltage
    internal: false
    accuracy_decimals: 3
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${chip_name}_Current CH1 CHGN"
    id: chgn_reference_value
    accuracy_decimals: 0
    update_interval: 1s
    lambda: |-
      float ch1 = id(${chip_name}_ch1_current).state;
      float unit1 = id(unit1_current).state;
      if (unit1 == 0) {
        return NAN; // 避免除以零
      }
      float err = (ch1 - unit1) / unit1;
      float result = (-err / (1 + err)) * 65536.0f;
      return (int32_t)result;
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${chip_name}_Voltage CHGN"
    id: voltage_chgn_reference_value
    accuracy_decimals: 0
    update_interval: 1s
    lambda: |-
      float ch1 = id(${chip_name}_voltage).state;
      float unit1 = id(unit1_voltage).state;
      if (unit1 == 0) {
        return NAN; // 避免除以零
      }
      float err = (ch1 - unit1) / unit1;
      float result = (-err / (1 + err)) * 65536.0f;
      return (int32_t)result;
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${chip_name}_Voltage Error"
    id: ${chip_name}_voltage_error
    accuracy_decimals: 3
    update_interval: 1s
    lambda: 
      return ((id(${chip_name}_voltage).state - id(unit1_voltage).state) / id(unit1_voltage).state) * 100.0;
    unit_of_measurement: "%"
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${chip_name}_Current Error"
    id: ${chip_name}_current_error
    accuracy_decimals: 3
    update_interval: 1s
    lambda: 
      return ((id(${chip_name}_ch1_current).state - id(unit1_current).state) / id(unit1_current).state) * 100.0;
    unit_of_measurement: "%"
    web_server: 
      sorting_group_id: calibrate

button:
# 批量设置所有CHGN值
  - platform: template
    name: "批量更新所有${chip_id}相CHGN值"
    id: update_all_${chip_id}_chgn_button
    on_press:
      - lambda: |-
          // 调用BL0906Factory的批量修改方法
          id(${chip_id})->update_all_chgn_values_from_sensor(); 
    web_server: 
      sorting_group_id: calibrate
  - platform: template
    name: "${chip_id}相CHGN 0"
    on_press:
      - lambda: id(${chip_id})->reset_all_chgn_values_to_zero();
    web_server: 
      sorting_group_id: calibrate
  # 批量清零RMSOS值
  - platform: template
    name: "${chip_id}相RMSOS 0"
    on_press:
      - lambda: id(${chip_id})->reset_all_rmsos_values_to_zero();
    web_server: 
      sorting_group_id: calibrate