@echo off
setlocal EnableDelayedExpansion

echo ================================================
echo PATH Issues Diagnostic Tool
echo ================================================
echo.

echo [INFO] Analyzing PATH environment variable...
echo.

REM Check if PATH contains problematic entries
echo [STEP] Checking for common problematic PATH entries...

set "PROBLEMATIC_FOUND=0"

REM Check for NVIDIA paths with spaces or special characters
echo %PATH% | findstr /i "nvidia" >nul
if not errorlevel 1 (
    echo [WARNING] Found NVIDIA-related paths in PATH
    set "PROBLEMATIC_FOUND=1"
)

REM Check for paths with parentheses
echo %PATH% | findstr "(" >nul
if not errorlevel 1 (
    echo [WARNING] Found paths with parentheses in PATH
    set "PROBLEMATIC_FOUND=1"
)

REM Check for paths with spaces (common issue)
echo %PATH% | findstr " " >nul
if not errorlevel 1 (
    echo [WARNING] Found paths with spaces in PATH
    set "PROBLEMATIC_FOUND=1"
)

if "%PROBLEMATIC_FOUND%"=="1" (
    echo.
    echo [ANALYSIS] Potential PATH issues detected
    echo These can cause "此时不应有 \NVIDIA" type errors
) else (
    echo [INFO] No obvious PATH issues detected
)

echo.
echo [STEP] Checking ESP-IDF environment...

if "%IDF_PATH%"=="" (
    echo [ERROR] IDF_PATH not set
    echo [INFO] ESP-IDF environment not configured
) else (
    echo [INFO] IDF_PATH: %IDF_PATH%
    
    REM Check if ESP-IDF export.bat exists
    if exist "%IDF_PATH%\export.bat" (
        echo [INFO] ESP-IDF export.bat found
    ) else (
        echo [WARNING] ESP-IDF export.bat not found at expected location
    )
)

echo.
echo [STEP] Checking for ESP32 toolchain...

REM Try different methods to find the compiler
set "COMPILER_FOUND=0"

REM Method 1: Direct PATH search
where xtensa-esp32-elf-g++.exe >nul 2>&1
if not errorlevel 1 (
    echo [INFO] ESP32 compiler found in PATH
    set "COMPILER_FOUND=1"
    for /f "tokens=*" %%i in ('where xtensa-esp32-elf-g++.exe 2^>nul') do (
        echo [INFO] Compiler location: %%i
        goto :found_compiler
    )
    :found_compiler
)

REM Method 2: Check common ESP-IDF locations
if "%COMPILER_FOUND%"=="0" (
    echo [INFO] Searching in common ESP-IDF locations...
    
    for %%D in (
        "C:\Espressif\tools\xtensa-esp32-elf"
        "%USERPROFILE%\.espressif\tools\xtensa-esp32-elf"
        "%IDF_PATH%\tools\xtensa-esp32-elf"
    ) do (
        if exist "%%D" (
            echo [INFO] Found ESP32 tools directory: %%D
            for /f %%F in ('dir /b "%%D" 2^>nul') do (
                if exist "%%D\%%F\xtensa-esp32-elf\bin\xtensa-esp32-elf-g++.exe" (
                    echo [INFO] Found compiler: %%D\%%F\xtensa-esp32-elf\bin\
                    set "COMPILER_FOUND=1"
                    goto :compiler_search_done
                )
            )
        )
    )
    :compiler_search_done
)

if "%COMPILER_FOUND%"=="0" (
    echo [ERROR] ESP32 toolchain not found
    echo [INFO] This indicates ESP-IDF is not properly configured
)

echo.
echo [STEP] Recommendations...

if "%PROBLEMATIC_FOUND%"=="1" (
    echo.
    echo [SOLUTION] To fix PATH-related issues:
    echo 1. Use the robust build script: build_precompiled_lib_robust.bat
    echo 2. Or temporarily clean PATH before running ESP-IDF export.bat
    echo.
    echo [ALTERNATIVE] Create a clean environment:
    echo 1. Open a new Command Prompt
    echo 2. Run: set PATH=C:\Windows\system32;C:\Windows
    echo 3. Run: [ESP-IDF-PATH]\export.bat
    echo 4. Run the build script
)

if "%COMPILER_FOUND%"=="0" (
    echo.
    echo [SOLUTION] To fix ESP-IDF issues:
    echo 1. Install ESP-IDF from: https://dl.espressif.com/dl/esp-idf-installer/
    echo 2. Run the export.bat script from ESP-IDF installation
    echo 3. Verify with: xtensa-esp32-elf-g++.exe --version
)

echo.
echo [STEP] Quick test...

echo [INFO] Testing basic command execution...

REM Test if we can run basic commands without PATH issues
echo test > test_file.tmp 2>nul
if exist test_file.tmp (
    echo [INFO] Basic file operations work
    del test_file.tmp >nul 2>&1
) else (
    echo [WARNING] Basic file operations may have issues
)

REM Test compiler if available
if "%COMPILER_FOUND%"=="1" (
    echo [INFO] Testing compiler execution...
    xtensa-esp32-elf-g++.exe --version >nul 2>&1
    if not errorlevel 1 (
        echo [INFO] Compiler executes successfully
    ) else (
        echo [WARNING] Compiler execution failed
    )
)

echo.
echo ================================================
echo Diagnostic Summary
echo ================================================

if "%PROBLEMATIC_FOUND%"=="1" (
    echo [ISSUE] PATH contains potentially problematic entries
    echo [RECOMMENDATION] Use build_precompiled_lib_robust.bat
) else (
    echo [OK] No obvious PATH issues detected
)

if "%COMPILER_FOUND%"=="1" (
    echo [OK] ESP32 toolchain is available
    echo [RECOMMENDATION] You can proceed with building
) else (
    echo [ISSUE] ESP32 toolchain not found
    echo [RECOMMENDATION] Install and configure ESP-IDF first
)

echo.
echo [NEXT STEPS]
if "%COMPILER_FOUND%"=="1" (
    echo Run: build_precompiled_lib_robust.bat
) else (
    echo 1. Install ESP-IDF
    echo 2. Run ESP-IDF export.bat
    echo 3. Run this diagnostic again
    echo 4. Run: build_precompiled_lib_robust.bat
)

echo.
pause 