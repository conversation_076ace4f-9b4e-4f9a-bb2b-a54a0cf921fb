# BL0910 寄存器列表整理

## 4.1 电参量寄存器

### 电流波形寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 1    | I[1]_WAVE    | 24   | 0x000000 | 通道 1 波形寄存器               |
| 2    | I[2]_WAVE    | 24   | 0x000000 | 通道 2 波形寄存器               |
| 3    | I[3]_WAVE    | 24   | 0x000000 | 通道 3 波形寄存器               |
| 4    | I[4]_WAVE    | 24   | 0x000000 | 通道 4 波形寄存器               |
| 5    | I[5]_WAVE    | 24   | 0x000000 | 通道 5 波形寄存器               |
| 6    | I[6]_WAVE    | 24   | 0x000000 | 通道 6 波形寄存器               |
| 7    | I[7]_WAVE    | 24   | 0x000000 | 通道 7 波形寄存器               |
| 8    | I[8]_WAVE    | 24   | 0x000000 | 通道 8 波形寄存器               |
| 9    | I[9]_WAVE    | 24   | 0x000000 | 通道 9 波形寄存器               |
| A    | I[10]_WAVE   | 24   | 0x000000 | 通道 10 波形寄存器              |

### 电压波形寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| B    | V_WAVE       | 24   | 0x000000 | 通道 11 波形寄存器              |

### 电流有效值寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| C    | I[1]_RMS     | 24   | 0x000000 | 通道 1 有效值寄存器             |
| D    | I[2]_RMS     | 24   | 0x000000 | 通道 2 有效值寄存器             |
| E    | I[3]_RMS     | 24   | 0x000000 | 通道 3 有效值寄存器             |
| F    | I[4]_RMS     | 24   | 0x000000 | 通道 4 有效值寄存器             |
| 10   | I[5]_RMS     | 24   | 0x000000 | 通道 5 有效值寄存器             |
| 11   | I[6]_RMS     | 24   | 0x000000 | 通道 6 有效值寄存器             |
| 12   | I[7]_RMS     | 24   | 0x000000 | 通道 7 有效值寄存器             |
| 13   | I[8]_RMS     | 24   | 0x000000 | 通道 8 有效值寄存器             |
| 14   | I[9]_RMS     | 24   | 0x000000 | 通道 9 有效值寄存器             |
| 15   | I[10]_RMS    | 24   | 0x000000 | 通道 10 有效值寄存器            |

### 电压有效值寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 16   | V_RMS        | 24   | 0x000000 | 通道 11 有效值寄存器            |

### 快速有效值寄存器
| 地址 | 名称                 | 位宽 | 默认值   | 描述                           |
|------|----------------------|------|----------|--------------------------------|
| 17   | I[1]_FAST_RMS        | 24   | 0x000000 | 通道 1 快速有效值寄存器         |
| 18   | I[2]_FAST_RMS        | 24   | 0x000000 | 通道 2 快速有效值寄存器         |
| 19   | I[3]_FAST_RMS        | 24   | 0x000000 | 通道 3 快速有效值寄存器         |
| 1A   | I[4]_FAST_RMS        | 24   | 0x000000 | 通道 4 快速有效值寄存器         |
| 1B   | I[5]_FAST_RMS        | 24   | 0x000000 | 通道 5 快速有效值寄存器         |
| 1C   | I[6]_FAST_RMS        | 24   | 0x000000 | 通道 6 快速有效值寄存器         |
| 1D   | I[7]_FAST_RMS        | 24   | 0x000000 | 通道 7 快速有效值寄存器         |
| 1E   | I[8]_FAST_RMS        | 24   | 0x000000 | 通道 8 快速有效值寄存器         |
| 1F   | I[9]_FAST_RMS        | 24   | 0x000000 | 通道 9 快速有效值寄存器         |
| 20   | I[10]_FAST_RMS       | 24   | 0x000000 | 通道 10 快速有效值寄存器        |
| 21   | V_FAST_RMS           | 24   | 0x000000 | 通道 11 快速有效值寄存器        |

### 有功功率寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 22   | WATT[1]      | 24   | 0x000000 | 通道 1 有功功率寄存器           |
| 23   | WATT[2]      | 24   | 0x000000 | 通道 2 有功功率寄存器           |
| 24   | WATT[3]      | 24   | 0x000000 | 通道 3 有功功率寄存器           |
| 25   | WATT[4]      | 24   | 0x000000 | 通道 4 有功功率寄存器           |
| 26   | WATT[5]      | 24   | 0x000000 | 通道 5 有功功率寄存器           |
| 27   | WATT[6]      | 24   | 0x000000 | 通道 6 有功功率寄存器           |
| 28   | WATT[7]      | 24   | 0x000000 | 通道 7 有功功率寄存器           |
| 29   | WATT[8]      | 24   | 0x000000 | 通道 8 有功功率寄存器           |
| 2A   | WATT[9]      | 24   | 0x000000 | 通道 9 有功功率寄存器           |
| 2B   | WATT[10]     | 24   | 0x000000 | 通道 10 有功功率寄存器          |
| 2C   | WATT         | 24   | 0x000000 | 总有功功率寄存器                |

### 无功功率寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 2D   | VAR          | 24   | 0x000000 | 可选通道无功功率寄存器          |
| 5D   | VAR          | 24   | 0x000000 | 可选通道（全波）无功功率寄存器  |

### 视在功率寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 2E   | VA           | 24   | 0x000000 | 可选通道视在功率寄存器          |

### 有功脉冲计数寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 2F   | CF[1]_CNT    | 24   | 0x000000 | 通道 1 有功脉冲计数             |
| 30   | CF[2]_CNT    | 24   | 0x000000 | 通道 2 有功脉冲计数             |
| 31   | CF[3]_CNT    | 24   | 0x000000 | 通道 3 有功脉冲计数             |
| 32   | CF[4]_CNT    | 24   | 0x000000 | 通道 4 有功脉冲计数             |
| 33   | CF[5]_CNT    | 24   | 0x000000 | 通道 5 有功脉冲计数             |
| 34   | CF[6]_CNT    | 24   | 0x000000 | 通道 6 有功脉冲计数             |
| 35   | CF[7]_CNT    | 24   | 0x000000 | 通道 7 有功脉冲计数             |
| 36   | CF[8]_CNT    | 24   | 0x000000 | 通道 8 有功脉冲计数             |
| 37   | CF[9]_CNT    | 24   | 0x000000 | 通道 9 有功脉冲计数             |
| 38   | CF[10]_CNT   | 24   | 0x000000 | 通道 10 有功脉冲计数            |
| 39   | CF_CNT       | 24   | 0x000000 | 总有功脉冲计数                  |

### 无功脉冲计数寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 3A   | CFQ_CNT      | 24   | 0x000000 | 可选通道无功脉冲计数            |

### 视在脉冲计数寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 3B   | CFS_CNT      | 24   | 0x000000 | 可选通道视在脉冲计数            |

### 电流电压波形夹角寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 3C   | ANGLE[1]     | 16   | 0x000000 | 通道 1 电流电压波形夹角寄存器    |
| 3D   | ANGLE[2]     | 16   | 0x000000 | 通道 2 电流电压波形夹角寄存器    |
| 3E   | ANGLE[3]     | 16   | 0x000000 | 通道 3 电流电压波形夹角寄存器    |
| 3F   | ANGLE[4]     | 16   | 0x000000 | 通道 4 电流电压波形夹角寄存器    |
| 40   | ANGLE[5]     | 16   | 0x000000 | 通道 5 电流电压波形夹角寄存器    |
| 41   | ANGLE[6]     | 16   | 0x000000 | 通道 6 电流电压波形夹角寄存器    |
| 42   | ANGLE[7]     | 16   | 0x000000 | 通道 7 电流电压波形夹角寄存器    |
| 43   | ANGLE[8]     | 16   | 0x000000 | 通道 8 电流电压波形夹角寄存器    |
| 44   | ANGLE[9]     | 16   | 0x000000 | 通道 9 电流电压波形夹角寄存器    |
| 45   | ANGLE[10]    | 16   | 0x000000 | 通道 10 电流电压波形夹角寄存器   |

### 其他寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 46   | I[1]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 1 快速有效值寄存器，保持                                         |
| 47   | I[2]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 2 快速有效值寄存器，保持                                         |
| 48   | I[3]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 3 快速有效值寄存器，保持                                         |
| 49   | I[4]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 4 快速有效值寄存器，保持                                         |
| 4A   | PF                       | 24   | 0x000000 | 可选通道功率因子寄存器                                                |
| 4B   | LINE_WATTHR              | 24   | 0        | 线周期累计有功能量寄存器                                              |
| 4C   | LINE_VARHR               | 24   | 0        | 线周期累计无功能量寄存器                                              |
| 4D   | SIGN                     | 24   | 0x000000 | 功率符号位。对应当前电能脉冲计数的符号位，在出 CF 脉冲时刷新         |
| 4E   | PERIOD                   | 20   | 0x000000 | 线电压频率周期寄存器（可选通道）                                    |
| 4F   | 空                       | -    | -        |                                                                      |
| 50   | 空                       | -    | -        |                                                                      |
| 51   | 空                       | -    | -        |                                                                      |
| 52   | 空                       | -    | -        |                                                                      |
| 53   | 空                       | -    | -        |                                                                      |
| 54   | STATUS1                  | 24   | 0x000000 | 中断状态寄存器 1                                                      |
| 55   | 空                       | -    | -        |                                                                      |
| 56   | STATUS3                  | 10   | 0x000    | M 状态寄存器                                                          |
| 57   | I[5]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 5 快速有效值寄存器，保持                                         |
| 58   | I[6]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 6 快速有效值寄存器，保持                                         |
| 59   | I[7]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 7 快速有效值寄存器，保持                                         |
| 5A   | I[8]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 8 快速有效值寄存器，保持                                         |
| 5B   | I[9]_FAST_RMS_HOLD       | 24   | 0x000000 | 通道 9 快速有效值寄存器，保持                                         |
| 5C   | I[10]_FAST_RMS_HOLD      | 24   | 0x000000 | 通道 10 快速有效值寄存器，保持                                        |
| 5E   | TPS1                     | 10   | 0x000000 | 内部温度值寄存器                                                      |
| 5F   | TPS2                     | 10   | 0x000000 | 外部温度值寄存器                                                      |

## 4.2 校表寄存器（外部写）

### 增益调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                                                                 |
|------|--------------|------|----------|----------------------------------------------------------------------|
| 60   | GAIN1        | 24   | 0x000000 | 通道 PGA 增益调整寄存器<br>0000=1；0001=2；0010=8；0011=16；          |
| 61   | GAIN2        | 20   | 0x00000  | 通道 PGA 增益调整寄存器<br>0000=1；0001=2；0010=8；0011=16；          |

### 相位校正寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 64   | PHASE[1]/PHASE[2]        | 16   | 0x0000   | [15:8] 对应通道 1 相位校正<br>[7:0] 对应通道 2 相位校正              |
| 65   | PHASE[3]/PHASE[4]        | 16   | 0x0000   | [15:8] 对应通道 3 相位校正<br>[7:0] 对应通道 4 相位校正              |
| 66   | PHASE[5]/PHASE[6]        | 16   | 0x0000   | [15:8] 对应通道 5 相位校正<br>[7:0] 对应通道 6 相位校正              |
| 67   | PHASE[7]/PHASE[8]        | 16   | 0x0000   | [15:8] 对应通道 7 相位校正<br>[7:0] 对应通道 8 相位校正              |
| 68   | PHASE[9]/PHASE[10]       | 16   | 0x0000   | [15:8] 对应通道 9 相位校正<br>[7:0] 对应通道 10 相位校正             |

### 相位校正寄存器（续）
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 69   | PHASE[11]                | 8    | 0x00     | [7:0] 用于校正的电压通道相位                                        |
| 6A   | VAR_PHCAL_I              | 5    | 0000H    | 电流通道无功相位校正                                                  |
| 6B   | VAR_PHCAL_V              | 5    | 0000H    | 电压通道无功相位校正                                                  |

### 有效值增益调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 6C   | RMSGN[1]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 6D   | RMSGN[2]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 6E   | RMSGN[3]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 6F   | RMSGN[4]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 70   | RMSGN[5]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 71   | RMSGN[6]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 72   | RMSGN[7]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 73   | RMSGN[8]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 74   | RMSGN[9]     | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 75   | RMSGN[10]    | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |
| 76   | RMSGN[11]    | 16   | 0x0000   | 对应通道有效值增益调整寄存器  |

### 有效值偏置校正寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 77   | RMSOS[1]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 78   | RMSOS[2]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 79   | RMSOS[3]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 7A   | RMSOS[4]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 7B   | RMSOS[5]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 7C   | RMSOS[6]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 7D   | RMSOS[7]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 7E   | RMSOS[8]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 7F   | RMSOS[9]     | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 80   | RMSOS[10]    | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |
| 81   | RMSOS[11]    | 24   | 0x000000 | 对应通道有效值偏置校正寄存器  |

### 小信号补偿寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 82   | WA_LOS[1]/WA_LOS[2]      | 24   | 0x000000 | [23:12] 对应(N-1) 有功小信号补偿寄存器，补码<br>[11:0] 对应(N) 有功小信号补偿寄存器，补码 |
| 83   | WA_LOS[3]/WA_LOS[4]      | 24   | 0x000000 | [23:12] 对应(N-1) 有功小信号补偿寄存器，补码<br>[11:0] 对应(N) 有功小信号补偿寄存器，补码 |
| 84   | WA_LOS[5]/WA_LOS[6]      | 24   | 0x000000 | [23:12] 对应(N-1) 有功小信号补偿寄存器，补码<br>[11:0] 对应(N) 有功小信号补偿寄存器，补码 |
| 85   | WA_LOS[7]/WA_LOS[8]      | 24   | 0x000000 | [23:12] 对应(N-1) 有功小信号补偿寄存器，补码<br>[11:0] 对应(N) 有功小信号补偿寄存器，补码 |
| 86   | WA_LOS[9]/WA_LOS[10]     | 24   | 0x000000 | [23:12] 对应(N-1) 有功小信号补偿寄存器，补码<br>[11:0] 对应(N) 有功小信号补偿寄存器，补码 |
| 87   | VAR_LOS/FVAR_LOS         | 24   | 0x000    | [11:0] 对应无功功小信号补偿寄存器，补码                              |

### 防潜动功率阈值寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 88   | VAR_CREEP/WA_CREEP       | 24   | 0x04C04C | [11:0] 为有功防潜动功率阈值<br>[23:12] 为无功防潜动功率阈值          |
| 89   | WA_CREEP2                | 12   | 0x000    | [11:0] 总有功防潜动阈值寄存器                                       |

### 小信号阈值寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 8A   | RMS_CREEP    | 12   | 0x200    | [11:0] 为有效值小信号阈值寄存器|

### 快速有效值控制寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 8B   | FAST_RMS_CTRL            | 24   | 0x20FFFF | [23:21] 通道快速有效值寄存器刷新时间, 可选半周波和 N 周波，默认是周波<br>[20:0] 通道快速有效值阈值寄存器 |

### 峰值门限寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 8C   | I_PKLVL/V_PKLVL          | 24   | 0xFFFFF  | [23:12] 电流峰值门限寄存器<br>[11:0] 电压峰值门限寄存器              |

### 过零超时寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 8E   | SAGCYC/ZXTOUT            | 24   | 0x04FFFF | [23:16] 跌落线周期寄存器<br>[15:0] 过零超时寄存器                    |

### 累加周期数寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 8F   | SAGLVL/LINECYC           | 24   | 0x100009 | [23:12] 跌落电压阈值寄存器<br>[11:0] 线能量累加周期数寄存器           |

### 主控输出状态寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 90   | flag_ctrl    | 24   | 0x000000 | 主控直接控制M1~M6 输出的电平状态|

### 过流指示控制寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 91   | flag_ctrl1               | 24   | 0x000000 | [23:10] 断开延时计时，0.1ms/lsb<br>[9:0] 指示控制，M10-M1：0-输出实时中断；1-输出延时控制 |
| 92   | flag_ctrl2               | 24   | 0x000000 | [23:10] 闭合延时计时，0.1ms/lsb<br>[9:0] 闭合控制，M10-M1：0-闭合，1-断开 |

### ADC 使能控制寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 93   | ADC_PD       | 11   | 0x000000 | 11 个通道 ADC 的使能控制        |

### 测温控制寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                                                                 |
|------|--------------------------|------|----------|----------------------------------------------------------------------|
| 94   | TPS_CTRL                 | 16   | 0x07FF   | [15] 测温开关<br>[14] 外部测温报警解除开关<br>[13:12] 测温选择<br>[11:10] 测温开启时间间隔选择<br>[9:0] 外部测温报警阀值设置 |

### 温度传感器系数校正寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                           |
|------|--------------------------|------|----------|--------------------------------|
| 95   | TPS2_A/TPS2_B            | 24   | 0x0000   | 外部温度传感器系数 A/B 校正寄存器|

### 用户模式选择寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 96   | MODE1        | 24   | 0x000000 | 用户模式选择寄存器 1            |
| 97   | MODE2        | 24   | 0x000000 | 用户模式选择寄存器 2            |
| 98   | MODE3        | 24   | 0x000000 | 用户模式选择寄存器 3            |

### 中断屏蔽寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| 9A   | MASK1        | 24   | 0x000000 | 控制一个中断是否产生一个有效的 IRQ1 输出 |

### 软件复位寄存器
| 地址 | 名称                     | 位宽 | 默认值   | 描述                           |
|------|--------------------------|------|----------|--------------------------------|
| 9F   | SOFT_RESET               | 24   | 0x000000 | 当输入为 5A5A5A 时，系统复位<br>当输入为 55AA55 时，用户读写寄存器复位 |

## 4.3 OTP 寄存器

### 增益调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| A0   | CHGN[1]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A1   | CHGN[2]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A2   | CHGN[3]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A3   | CHGN[4]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A4   | CHGN[5]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A5   | CHGN[6]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A6   | CHGN[7]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A7   | CHGN[8]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A8   | CHGN[9]      | 16   | 0x0000   | 对应通道增益调整寄存器          |
| A9   | CHGN[10]     | 16   | 0x0000   | 对应通道增益调整寄存器          |
| AA   | CHGN[11]     | 16   | 0x0000   | 对应通道增益调整寄存器          |

### 偏置调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| AB   | CHOS[1]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| AC   | CHOS[2]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| AD   | CHOS[3]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| AE   | CHOS[4]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| AF   | CHOS[5]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| B0   | CHOS[6]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| B1   | CHOS[7]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| B2   | CHOS[8]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| B3   | CHOS[9]      | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| B4   | CHOS[10]     | 16   | 0x0000   | 对应通道偏置调整寄存器          |
| B5   | CHOS[11]     | 16   | 0x0000   | 对应通道偏置调整寄存器          |

### 有功功率增益调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| B6   | WATTGN[1]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| B7   | WATTGN[2]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| B8   | WATTGN[3]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| B9   | WATTGN[4]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| BA   | WATTGN[5]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| BB   | WATTGN[6]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| BC   | WATTGN[7]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| BD   | WATTGN[8]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| BE   | WATTGN[9]    | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |
| BF   | WATTGN[10]   | 16   | 0x0000   | 对应通道有功功率增益调整寄存器  |

### 有功功率偏置调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| C0   | WATTOS[1]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C1   | WATTOS[2]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C2   | WATTOS[3]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C3   | WATTOS[4]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C4   | WATTOS[5]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C5   | WATTOS[6]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C6   | WATTOS[7]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C7   | WATTOS[8]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C8   | WATTOS[9]    | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |
| C9   | WATTOS[10]   | 16   | 0x0000   | 对应通道有功功率偏置调整寄存器  |

### 无功功率增益调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| CA   | VARGN        | 16   | 0x0000   | 对应通道无功功率增益调整寄存器  |

### 无功功率偏置调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| CB   | VAROS        | 16   | 0x0000   | 对应通道无功功率偏置调整寄存器  |

### 视在功率增益调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| CC   | VAGN         | 16   | 0x0000   | 对应通道视在功率增益调整寄存器  |

### 视在功率偏置调整寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| CD   | VAOS         | 16   | 0x0000   | 对应通道视在功率偏置调整寄存器  |

### CF 缩放比例寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| CE   | CFDIV        | 12   | 0x010    | CF 缩放比例寄存器               |

### 保留寄存器
| 地址 | 名称         | 位宽 | 默认值   | 描述                           |
|------|--------------|------|----------|--------------------------------|
| CF   | RESERVE0     | 16   | 0x0000   | 保留                           |
| D0   | OTP checksum1| 16   | 0x00     | OTP 寄存器校验和                |

## 4.4 模式寄存器

### 模式寄存器 1 (MODE1)
| 地址 | 名称         | 位宽 | 默认值   | 描述                                                                 |
|------|--------------|------|----------|----------------------------------------------------------------------|
| 0x96 | MODE1        | -    | -        | 工作模式寄存器                                                       |
| No.  | name         | default value | description                                               |
| [10:0] | WAVE_SEL   | 11{1'b0} | 有功波形选择：0-选择高通,全波，1-选择低通<br>2，直流                |
| [21:11] | WAVEF_SEL | 11{1'b0} | 有功波形选择：0-选择低通 1，基波，1-选择<br>低通 2，直流             |
| [22] | L_F_SEL     | 1'b0      | 快速有效值选择通过高通，默认为0 选择没有<br>高通，为 1 选择高通        |
| [23] | WAVE_REG_SEL| 1'b0      | 电流 WAVE 波形寄存器输出选择，默认 0 选择正常电流通道的波形，为 1 选择快速有效值的波形输出 |

### 模式寄存器 2 (MODE2)
| 地址 | 名称         | 位宽 | 默认值   | 描述                                                                 |
|------|--------------|------|----------|----------------------------------------------------------------------|
| 0x97 | MODE2        | -    | -        | 工作模式寄存器                                                       |
| No.  | name         | default value | description                                               |
| [21:0] | WAVE_RMS_SEL | 11{2'b00} | 有效值波形选择,00-交流,10-直流,01-选择基波,11-选择全波               |
| [22] | RMS_UPDATE_SEL | 1'b0       | 慢速有效值存器更新速度选择,0b1 为1.05s,0b0 为 525ms,默认选择 525ms; |
| [23] | AC_FREQ_SEL    | 1'b0       | 交流电频率选择，0b1 为 60Hz，0b0 为 50Hz，默认选择 50Hz              |

### 模式寄存器 3 (MODE3)
| 地址 | 名称         | 位宽 | 默认值   | 描述                                                                 |
|------|--------------|------|----------|----------------------------------------------------------------------|
| 0x98 | MODE3        | -    | -        | 工作模式寄存器                                                       |
| No.  | name         | default value | description                                               |
| [3:0] | VAR_I_SEL   | 4'b000      | 选择无功电流计量通道                                                   |
| [8]   | add_sel     | 1'b0        | watt 总和加方式：0-绝对值加；1-代数和加                               |
| [9]   | cf_enable   | 1'b0        | 0-cf disable，默认；1-cf enable 并输出@m2 m3                          |
| [13:10] | CF_SEL    | 4'b0000     | 通道 CF_WATT 输出选择，默认关闭 CF                                     |
| [14] | hpf_sel     | 1'b0        | hpf 选择：0-使用 hpf；1-不用 hpf                                      |
| [15] | cf_add_sel  | 1'b0        | watt 和var 能量加方式：0-绝对值加；1-代数和加                         |
| [16] | var_sel     | 1'b0        | var 能量选择：0-基波；1-全波                                        |
