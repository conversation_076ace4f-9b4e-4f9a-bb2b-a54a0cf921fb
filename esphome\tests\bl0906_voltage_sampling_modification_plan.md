# BL0906 电压采样方式代码重构计划

## 背景分析

当前的 `bl0906_calibration.h` 文件中存在两套电压采样参数定义：

1. **电压互感器采样方式**（第一套参数）
   - 使用较大的分压电阻：`Rf = 100000.0f Ω`
   - 适用于高压电压测量
   - 计算公式：`Kv = (13162 * Gain_V * R46 * 1000) / (Vref * Rf)`

2. **电压分压电阻采样方式**（第二套参数）
   - 使用较小的分压电阻：`Rf = 1500.0f kΩ`
   - 包含分压比计算：`(Rf + Rv)`
   - 计算公式：`Kv = 13162.0f * Rv * 1000 * GAIN_V / (Vref * (Rf + Rv))`

## 问题识别

1. 当前代码存在参数重复定义问题
2. 两套参数混合存在，缺乏清晰的选择机制
3. 需要根据YAML配置动态选择采样方式
4. 代码结构需要重构以支持条件编译或运行时选择

## 修改计划（条件编译方式）

### 第一步：设计YAML配置接口
- 在BL0906组件的YAML配置中添加 `voltage_sampling_mode` 选项
- 支持两个值：`transformer`（默认）或 `resistor_divider`
- YAML配置解析时生成对应的条件编译宏定义

### 第二步：修改Python配置文件
- 更新 `components/bl0906/__init__.py` 文件
- 添加 `voltage_sampling_mode` 配置选项
- 实现配置解析和宏定义生成逻辑
- 设置默认值为 `transformer`

### 第三步：使用条件编译重构参数定义
- 使用 `#ifdef` 和 `#else` 来条件性定义参数
- 移除重复的参数定义
- 保持通用参数在条件编译块外部
- 添加默认值处理机制

### 第四步：条件编译校准系数计算
- 使用条件编译来选择对应的校准系数计算公式
- 确保每种模式下的计算公式正确
- 保持代码简洁，避免运行时判断开销

### 第五步：优化计算公式
- **互感器采样模式**：
  ```cpp
  Kv = (13162 * Gain_V * R46 * 1000) / (Vref * Rf)
  Ki = (12875 * Gain_I * (RL + RL) * 1000 / Rt) / Vref
  Kp = 2.3847e-7 * Ki * Kv
  ```

- **分压电阻采样模式**：
  ```cpp
  Kv = 13162.0f * Rv * 1000 * GAIN_V / (Vref * (Rf + Rv))
  Ki = 12875.0f * GAIN_I * 2 * RL * 1000 / Rt / Vref
  Kp = 2.3825e-8 * Kv * Ki
  ```

### 第六步：清理和优化代码结构
- 移除重复的参数定义和注释掉的旧代码
- 统一代码风格和命名规范
- 添加必要的文档注释说明条件编译的使用
- 确保所有计算都使用正确的数据类型（float/double）

### 第七步：更新相关组件文件
- 修改 `bl0906.cpp` 和 `bl0906.h` 文件
- 确保这些文件能正确使用条件编译的校准系数
- 添加必要的配置宏定义或包含

## 预期的代码结构（条件编译版本）

```cpp
#pragma once

namespace esphome {
namespace bl0906_factory {

// 通用参数定义
static constexpr float Vref = 1.097f;           // 内部参考电压 (V)
static constexpr int Gain_V = 1;                // 电压通道增益 (1, 2, 8, 16)
static constexpr int Gain_I = 1;                // 电流通道增益 (1, 2, 8, 16)
static constexpr float RL = 5.1f;               // 互感器副边负载电阻 (Ω)
static constexpr float Rt = 2000.0f;            // 互感器变比，如 2000:1

// 条件编译配置：BL0906_VOLTAGE_SAMPLING_TRANSFORMER 或 BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER
#ifdef BL0906_VOLTAGE_SAMPLING_TRANSFORMER
    // 电压互感器采样方式参数
    static constexpr float Rf = 100000.0f;      // 分压上拉电阻 (Ω)  
    static constexpr int R46 = 100;             // 电压采样电阻 (Ω)
    
    // 校准系数计算
    static constexpr float Ki = (12875 * Gain_I * (RL + RL) * 1000 / Rt) / Vref;
    static constexpr float Kv = (13162 * Gain_V * R46 * 1000) / (Vref * Rf);
    static constexpr float Kp = 2.3847e-7 * Ki * Kv;
    
#elif defined(BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER)
    // 电压分压电阻采样方式参数
    static constexpr float Rf = 1500.0f;        // 分压上拉电阻 (kΩ)
    static constexpr float Rv = 1.0f;            // 分压下拉电阻 (kΩ)
    
    // 校准系数计算
    static constexpr float Ki = 12875.0f * Gain_I * 2 * RL * 1000 / Rt / Vref;
    static constexpr float Kv = 13162.0f * Rv * 1000 * Gain_V / (Vref * (Rf + Rv));
    static constexpr float Kp = 2.3825e-8 * Kv * Ki;
    
#else
    #error "必须定义 BL0906_VOLTAGE_SAMPLING_TRANSFORMER 或 BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER"
#endif

// 通用校准系数（两种模式都使用）
static constexpr float Kp_sum = Kp / 16;
static constexpr float Ke = (3600000.0f * 16 * Kp) / (4194304.0f * 0.032768f * 16);  // 单位：kWh/pulse
static constexpr float Ke_sum = Ke / 16;
static constexpr float FREF = 1 / 10000000;     // 频率转换
static constexpr float TREF = 59 - 40 / 12.5;   // 温度转换

}  // namespace bl0906_factory
}  // namespace esphome
```

## YAML配置到条件编译的实现

### YAML配置示例
```yaml
# ESPHome配置文件
bl0906:
  # 电压采样方式配置（可选，默认为transformer）
  voltage_sampling_mode: transformer  # 或 resistor_divider
  # 其他配置...
```

### Python配置解析（在组件的 __init__.py 中）
```python
import esphome.codegen as cg
from esphome.const import CONF_ID

# 定义配置常量
CONF_VOLTAGE_SAMPLING_MODE = "voltage_sampling_mode"

# 定义枚举选择
VoltageSamplingMode = bl0906_ns.enum("VoltageSamplingMode")
VOLTAGE_SAMPLING_MODES = {
    "transformer": VoltageSamplingMode.TRANSFORMER,
    "resistor_divider": VoltageSamplingMode.RESISTOR_DIVIDER,
}

# 配置schema
CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Component),
    cv.Optional(CONF_VOLTAGE_SAMPLING_MODE, default="transformer"): cv.enum(
        VOLTAGE_SAMPLING_MODES, upper=False
    ),
    # 其他配置项...
})

# 代码生成
async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    
    # 根据配置生成条件编译宏
    sampling_mode = config[CONF_VOLTAGE_SAMPLING_MODE]
    if sampling_mode == "transformer":
        cg.add_define("BL0906_VOLTAGE_SAMPLING_TRANSFORMER")
    elif sampling_mode == "resistor_divider":
        cg.add_define("BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER")
    
    # 其他代码生成...
```

### 条件编译头文件
在 `bl0906_calibration.h` 中使用生成的宏：
```cpp
// 默认情况下使用电压互感器方式（如果没有明确定义）
#ifndef BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER
#ifndef BL0906_VOLTAGE_SAMPLING_TRANSFORMER
#define BL0906_VOLTAGE_SAMPLING_TRANSFORMER
#endif
#endif
```

## 风险评估

1. **兼容性风险**：由于不需要向后兼容，可以大胆重构
2. **配置风险**：需要确保编译时宏定义正确设置
3. **计算精度风险**：需要验证两种模式下的计算结果准确性

## 验证计划

1. 分别编译两种配置验证计算结果正确性
2. 使用实际硬件测试两种配置的准确性
3. 确保编译时错误检查机制正常工作（未定义宏时报错）

## 总结

采用条件编译的重构方案将：
- **消除代码重复**：只保留一份参数定义
- **提供简洁的配置机制**：通过编译时宏选择采样方式
- **保持代码高效**：避免运行时判断开销
- **支持编译时验证**：未正确配置时编译错误
- **提高代码可维护性**：结构清晰，易于理解和修改 