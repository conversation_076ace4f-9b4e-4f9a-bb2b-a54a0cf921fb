# BL0906与BL0910寄存器地址映射对比表

## 1. 电参量寄存器对比

### 1.1 电流波形寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x02 | 0x01 | I[1]_WAVE | 前移1位 |
| 2 | 0x03 | 0x02 | I[2]_WAVE | 前移1位 |
| 3 | 0x04 | 0x03 | I[3]_WAVE | 前移1位 |
| 4 | 0x05 | 0x04 | I[4]_WAVE | 前移1位 |
| 5 | 0x08 | 0x05 | I[5]_WAVE | 地址变化 |
| 6 | 0x09 | 0x06 | I[6]_WAVE | 地址变化 |
| 7 | - | 0x07 | I[7]_WAVE | 新增 |
| 8 | - | 0x08 | I[8]_WAVE | 新增 |
| 9 | - | 0x09 | I[9]_WAVE | 新增 |
| 10 | - | 0x0A | I[10]_WAVE | 新增 |
| 电压 | 0x0B | 0x0B | V_WAVE | 保持不变 |

### 1.2 电流有效值寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x0D | 0x0C | I[1]_RMS | 前移1位 |
| 2 | 0x0E | 0x0D | I[2]_RMS | 前移1位 |
| 3 | 0x0F | 0x0E | I[3]_RMS | 前移1位 |
| 4 | 0x10 | 0x0F | I[4]_RMS | 前移1位 |
| 5 | 0x13 | 0x10 | I[5]_RMS | 地址变化 |
| 6 | 0x14 | 0x11 | I[6]_RMS | 地址变化 |
| 7 | - | 0x12 | I[7]_RMS | 新增 |
| 8 | - | 0x13 | I[8]_RMS | 新增 |
| 9 | - | 0x14 | I[9]_RMS | 新增 |
| 10 | - | 0x15 | I[10]_RMS | 新增 |
| 电压 | 0x16 | 0x16 | V_RMS | 保持不变 |

### 1.3 快速有效值寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x18 | 0x17 | I[1]_FAST_RMS | 前移1位 |
| 2 | 0x19 | 0x18 | I[2]_FAST_RMS | 前移1位 |
| 3 | 0x1A | 0x19 | I[3]_FAST_RMS | 前移1位 |
| 4 | 0x1B | 0x1A | I[4]_FAST_RMS | 前移1位 |
| 5 | 0x1E | 0x1B | I[5]_FAST_RMS | 地址变化 |
| 6 | 0x1F | 0x1C | I[6]_FAST_RMS | 地址变化 |
| 7 | - | 0x1D | I[7]_FAST_RMS | 新增 |
| 8 | - | 0x1E | I[8]_FAST_RMS | 新增 |
| 9 | - | 0x1F | I[9]_FAST_RMS | 新增 |
| 10 | - | 0x20 | I[10]_FAST_RMS | 新增 |
| 电压 | 0x21 | 0x21 | V_FAST_RMS | 保持不变 |

### 1.4 有功功率寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x23 | 0x22 | WATT[1] | 前移1位 |
| 2 | 0x24 | 0x23 | WATT[2] | 前移1位 |
| 3 | 0x25 | 0x24 | WATT[3] | 前移1位 |
| 4 | 0x26 | 0x25 | WATT[4] | 前移1位 |
| 5 | 0x29 | 0x26 | WATT[5] | 地址变化 |
| 6 | 0x2A | 0x27 | WATT[6] | 地址变化 |
| 7 | - | 0x28 | WATT[7] | 新增 |
| 8 | - | 0x29 | WATT[8] | 新增 |
| 9 | - | 0x2A | WATT[9] | 新增 |
| 10 | - | 0x2B | WATT[10] | 新增 |
| 总功率 | 0x2C | 0x2C | WATT | 保持不变 |

### 1.5 有功脉冲计数寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x30 | 0x2F | CF[1]_CNT | 前移1位 |
| 2 | 0x31 | 0x30 | CF[2]_CNT | 前移1位 |
| 3 | 0x32 | 0x31 | CF[3]_CNT | 前移1位 |
| 4 | 0x33 | 0x32 | CF[4]_CNT | 前移1位 |
| 5 | 0x36 | 0x33 | CF[5]_CNT | 地址变化 |
| 6 | 0x37 | 0x34 | CF[6]_CNT | 地址变化 |
| 7 | - | 0x35 | CF[7]_CNT | 新增 |
| 8 | - | 0x36 | CF[8]_CNT | 新增 |
| 9 | - | 0x37 | CF[9]_CNT | 新增 |
| 10 | - | 0x38 | CF[10]_CNT | 新增 |
| 总和 | 0x39 | 0x39 | CF_CNT | 保持不变 |

### 1.6 电流电压波形夹角寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x3D | 0x3C | ANGLE[1] | 前移1位 |
| 2 | 0x3E | 0x3D | ANGLE[2] | 前移1位 |
| 3 | 0x3F | 0x3E | ANGLE[3] | 前移1位 |
| 4 | 0x40 | 0x3F | ANGLE[4] | 前移1位 |
| 5 | 0x43 | 0x40 | ANGLE[5] | 地址变化 |
| 6 | 0x44 | 0x41 | ANGLE[6] | 地址变化 |
| 7 | - | 0x42 | ANGLE[7] | 新增 |
| 8 | - | 0x43 | ANGLE[8] | 新增 |
| 9 | - | 0x44 | ANGLE[9] | 新增 |
| 10 | - | 0x45 | ANGLE[10] | 新增 |

### 1.7 其他测量寄存器

| 功能 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 线电压频率周期 | 0x4E | 0x4E | PERIOD | 保持不变 |
| 内部温度 | 0x5E | 0x5E | TPS1 | 保持不变 |

## 2. 校准寄存器对比

### 2.1 有效值增益调整寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x6D | 0x6C | RMSGN[1] | 前移1位 |
| 2 | 0x6E | 0x6D | RMSGN[2] | 前移1位 |
| 3 | 0x6F | 0x6E | RMSGN[3] | 前移1位 |
| 4 | 0x70 | 0x6F | RMSGN[4] | 前移1位 |
| 5 | 0x73 | 0x70 | RMSGN[5] | 地址变化 |
| 6 | 0x74 | 0x71 | RMSGN[6] | 地址变化 |
| 7 | - | 0x72 | RMSGN[7] | 新增 |
| 8 | - | 0x73 | RMSGN[8] | 新增 |
| 9 | - | 0x74 | RMSGN[9] | 新增 |
| 10 | - | 0x75 | RMSGN[10] | 新增 |
| 电压 | 0x76 | 0x76 | RMSGN[11] | 作为第11通道 |

### 2.2 有效值偏置校正寄存器

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0x78 | 0x77 | RMSOS[1] | 前移1位 |
| 2 | 0x79 | 0x78 | RMSOS[2] | 前移1位 |
| 3 | 0x7A | 0x79 | RMSOS[3] | 前移1位 |
| 4 | 0x7B | 0x7A | RMSOS[4] | 前移1位 |
| 5 | 0x7E | 0x7B | RMSOS[5] | 地址变化 |
| 6 | 0x7F | 0x7C | RMSOS[6] | 地址变化 |
| 7 | - | 0x7D | RMSOS[7] | 新增 |
| 8 | - | 0x7E | RMSOS[8] | 新增 |
| 9 | - | 0x7F | RMSOS[9] | 新增 |
| 10 | - | 0x80 | RMSOS[10] | 新增 |
| 电压 | 0x81 | 0x81 | RMSOS[11] | 作为第11通道 |

### 2.3 OTP寄存器（电流通道增益）

| 通道 | BL0906地址 | BL0910地址 | 寄存器名 | 说明 |
|------|-----------|-----------|----------|------|
| 1 | 0xA1 | 0xA0 | CHGN[1] | 前移1位 |
| 2 | 0xA2 | 0xA1 | CHGN[2] | 前移1位 |
| 3 | 0xA3 | 0xA2 | CHGN[3] | 前移1位 |
| 4 | 0xA4 | 0xA3 | CHGN[4] | 前移1位 |
| 5 | 0xA7 | 0xA4 | CHGN[5] | 地址变化 |
| 6 | 0xA8 | 0xA5 | CHGN[6] | 地址变化 |
| 7 | - | 0xA6 | CHGN[7] | 新增 |
| 8 | - | 0xA7 | CHGN[8] | 新增 |
| 9 | - | 0xA8 | CHGN[9] | 新增 |
| 10 | - | 0xA9 | CHGN[10] | 新增 |
| 电压 | 0xAA | 0xAA | CHGN[11] | 作为第11通道 |

## 3. 重要注意事项

### 3.1 地址偏移模式
- **通道1-4**：地址普遍前移1位
- **通道5-6**：地址发生较大变化
- **通道7-10**：全新地址
- **特殊寄存器**：保持不变（如总功率、电压等）

### 3.2 数组定义建议
```cpp
// BL0910寄存器地址数组定义
static constexpr uint8_t BL0910_I_RMS[10] = {
    0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15
};

static constexpr uint8_t BL0910_WATT[10] = {
    0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B
};

static constexpr uint8_t BL0910_CF_CNT[10] = {
    0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38
};
```

### 3.3 兼容性策略
1. **保持接口一致**：用户配置语法不变
2. **自动扩展**：自动支持10通道，向下兼容6通道配置
3. **渐进式配置**：用户可以只配置需要的通道
4. **错误处理**：对超出范围的通道请求提供清晰的错误信息

这个映射表将作为BL0910CA组件开发的核心参考文档，确保所有寄存器地址转换的准确性。 