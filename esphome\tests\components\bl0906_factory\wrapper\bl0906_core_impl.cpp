// BL0906 核心算法实现 - 真实预编译库
// 这个文件包含从 bl0906_factory.cpp 提取的核心算法
// 在实际部署中，这个文件将被编译为预编译库 libbl0906_core.a

#include "bl0906_core_api.h"
#include <string.h>
#include <math.h>

// ============================================================================
// 内部状态和数据结构（在预编译库中会被隐藏）
// ============================================================================

static bl0906_chip_model_t g_chip_model = BL0906_CHIP_BL0906;
static bl0906_comm_type_t g_comm_type = BL0906_COMM_UART;
static bl0906_comm_callbacks_t g_callbacks = {0};
static bool g_initialized = false;

// ============================================================================
// 芯片参数定义（从 bl0906_chip_params.h 迁移）
// ============================================================================

// BL0906 地址数组
static const uint8_t BL0906_I_RMS_ADDRS[] = {0x0D, 0x0E, 0x0F, 0x10, 0x13, 0x14};
static const uint8_t BL0906_WATT_ADDRS[] = {0x23, 0x24, 0x25, 0x26, 0x29, 0x2A};
static const uint8_t BL0906_CF_CNT_ADDRS[] = {0x30, 0x31, 0x32, 0x33, 0x36, 0x37};
static const uint8_t BL0906_RMSGN_ADDRS[] = {0x6D, 0x6E, 0x6F, 0x70, 0x73, 0x74};
static const uint8_t BL0906_RMSOS_ADDRS[] = {0x78, 0x79, 0x7A, 0x7B, 0x7E, 0x7F};
static const uint8_t BL0906_CHGN_ADDRS[] = {0xA1, 0xA2, 0xA3, 0xA4, 0xA7, 0xA8};
static const uint8_t BL0906_CHOS_ADDRS[] = {0xAC, 0xAD, 0xAE, 0xAF, 0xB2, 0xB3};
static const uint8_t BL0906_WATTGN_ADDRS[] = {0xB7, 0xB8, 0xB9, 0xBA, 0xBD, 0xBE};
static const uint8_t BL0906_WATTOS_ADDRS[] = {0xC1, 0xC2, 0xC3, 0xC4, 0xC7, 0xC8};

// BL0910 地址数组
static const uint8_t BL0910_I_RMS_ADDRS[] = {0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15};
static const uint8_t BL0910_WATT_ADDRS[] = {0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B};
static const uint8_t BL0910_CF_CNT_ADDRS[] = {0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38};
static const uint8_t BL0910_RMSGN_ADDRS[] = {0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75};
static const uint8_t BL0910_RMSOS_ADDRS[] = {0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F, 0x80};
static const uint8_t BL0910_CHGN_ADDRS[] = {0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9};
static const uint8_t BL0910_CHOS_ADDRS[] = {0xAC, 0xAD, 0xAE, 0xAF, 0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5};
static const uint8_t BL0910_WATTGN_ADDRS[] = {0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF};
static const uint8_t BL0910_WATTOS_ADDRS[] = {0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9};

// 芯片参数结构
typedef struct {
    uint8_t channel_count;
    const uint8_t* i_rms_addr;
    const uint8_t* watt_addr;
    const uint8_t* cf_cnt_addr;
    const uint8_t* rmsgn_addr;
    const uint8_t* rmsos_addr;
    const uint8_t* chgn_addr;
    const uint8_t* chos_addr;
    const uint8_t* wattgn_addr;
    const uint8_t* wattos_addr;
    const char* chip_name;
} chip_params_internal_t;

// 芯片参数查找表
static const chip_params_internal_t CHIP_PARAMS[] = {
    // BL0906
    {6, BL0906_I_RMS_ADDRS, BL0906_WATT_ADDRS, BL0906_CF_CNT_ADDRS, BL0906_RMSGN_ADDRS, 
     BL0906_RMSOS_ADDRS, BL0906_CHGN_ADDRS, BL0906_CHOS_ADDRS, BL0906_WATTGN_ADDRS, 
     BL0906_WATTOS_ADDRS, "BL0906"},
    // BL0910
    {10, BL0910_I_RMS_ADDRS, BL0910_WATT_ADDRS, BL0910_CF_CNT_ADDRS, BL0910_RMSGN_ADDRS,
     BL0910_RMSOS_ADDRS, BL0910_CHGN_ADDRS, BL0910_CHOS_ADDRS, BL0910_WATTGN_ADDRS,
     BL0910_WATTOS_ADDRS, "BL0910"}
};

// 通用寄存器地址常量
#define V_RMS_ADDR 0x16
#define FREQUENCY_ADDR 0x4E
#define TEMPERATURE_ADDR 0x5E
#define WATT_SUM_ADDR 0x2C
#define CF_SUM_ADDR 0x39
#define MODE2_ADDR 0x97
#define CHGN_V_ADDR 0xAA
#define CHOS_V_ADDR 0xB5
#define WR_PROTECT_REG 0x9E

// 操作命令定义
#define UART_READ_COMMAND 0x35
#define UART_WRITE_COMMAND 0xCA
#define SPI_READ_COMMAND 0x82
#define SPI_WRITE_COMMAND 0x81

// MODE2寄存器位掩码
#define MODE2_AC_FREQ_SEL_MASK 0x800000
#define MODE2_AC_FREQ_50HZ 0x000000
#define MODE2_AC_FREQ_60HZ 0x800000

// ============================================================================
// 校准系数定义（从 bl0906_calibration.h 迁移）
// ============================================================================

// 通用参数定义
static const float Vref = 1.097f;           // 内部参考电压 (V)
static const int Gain_V = 1;                // 电压通道增益 (1, 2, 8, 16)
static const int Gain_I = 1;                // 电流通道增益 (1, 2, 8, 16)
static const float RL = 5.1f;               // 互感器副边负载电阻 (Ω)
static const float Rt = 2000.0f;            // 互感器变比，如 2000:1

// 电压互感器采样方式参数
static const float Rf_transformer = 100000.0f;      // 分压上拉电阻 (Ω)  
static const int R46 = 100;                         // 电压采样电阻 (Ω)

// 电压分压电阻采样方式参数
static const float Rf_divider = 1500.0f;            // 分压上拉电阻 (kΩ)
static const float Rv_divider = 1.0f;               // 分压下拉电阻 (kΩ)

// 频率和温度转换常量
static const float FREF = 1.0f / 10000000.0f;       // 频率转换
static const float TREF = 59.0f - 40.0f / 12.5f;    // 温度转换

// ============================================================================
// 内部算法函数（从 bl0906_chip_params.h 迁移）
// ============================================================================

// 高性能内联地址查找函数
static uint8_t get_register_addr_internal(bl0906_chip_model_t chip, bl0906_register_type_t type, int channel) {
    const chip_params_internal_t* p = &CHIP_PARAMS[(uint8_t)chip];

    // 处理电压通道的特殊情况（channel = -1）
    if (channel == -1) {
        switch(type) {
            case BL0906_REG_CHGN:   return CHGN_V_ADDR; // 电压通道增益
            case BL0906_REG_CHOS:   return CHOS_V_ADDR; // 电压通道偏置
            case BL0906_REG_CHGN_V: return CHGN_V_ADDR; // 电压通道增益
            case BL0906_REG_CHOS_V: return CHOS_V_ADDR; // 电压通道偏置
            default: return 0; // 其他类型不支持电压通道
        }
    }

    // 处理普通通道
    if (channel < 0 || channel >= p->channel_count) return 0;

    switch(type) {
        case BL0906_REG_I_RMS:  return p->i_rms_addr[channel];
        case BL0906_REG_WATT:   return p->watt_addr[channel];
        case BL0906_REG_CF_CNT: return p->cf_cnt_addr[channel];
        case BL0906_REG_RMSGN:  return p->rmsgn_addr[channel];
        case BL0906_REG_RMSOS:  return p->rmsos_addr[channel];
        case BL0906_REG_CHGN:   return p->chgn_addr[channel];
        case BL0906_REG_CHOS:   return p->chos_addr[channel];
        case BL0906_REG_WATTGN: return p->wattgn_addr[channel];
        case BL0906_REG_WATTOS: return p->wattos_addr[channel];
        case BL0906_REG_CHGN_V: return CHGN_V_ADDR; // 电压通道增益（两芯片相同）
        case BL0906_REG_CHOS_V: return CHOS_V_ADDR; // 电压通道偏置（两芯片相同）
        case BL0906_REG_VOLTAGE: return V_RMS_ADDR;
        case BL0906_REG_FREQUENCY: return FREQUENCY_ADDR;
        case BL0906_REG_TEMPERATURE: return TEMPERATURE_ADDR;
        case BL0906_REG_WATT_SUM: return WATT_SUM_ADDR;
        case BL0906_REG_CF_SUM: return CF_SUM_ADDR;
        case BL0906_REG_MODE2: return MODE2_ADDR;
        default: return 0;
    }
}

// 寄存器地址验证函数
static bool is_valid_calibration_register_internal(bl0906_chip_model_t chip, uint8_t address) {
    const chip_params_internal_t* p = &CHIP_PARAMS[(uint8_t)chip];
    
    // 检查电压校准寄存器（两芯片通用）
    if (address == CHGN_V_ADDR || address == CHOS_V_ADDR) {
        return true;
    }
    
    // 检查各类型校准寄存器
    for (int ch = 0; ch < p->channel_count; ch++) {
        if (address == p->rmsgn_addr[ch] ||
            address == p->rmsos_addr[ch] ||
            address == p->chgn_addr[ch] ||
            address == p->chos_addr[ch] ||
            address == p->wattgn_addr[ch] ||
            address == p->wattos_addr[ch]) {
            return true;
        }
    }
    
    return false;
}

static bool is_valid_register_for_chip_internal(bl0906_chip_model_t chip, uint8_t address) {
    // 首先检查是否为校准寄存器
    if (is_valid_calibration_register_internal(chip, address)) {
        return true;
    }
    
    // 检查通用寄存器（两芯片都支持）
    if (address == V_RMS_ADDR ||           // 0x16 电压有效值
        address == WATT_SUM_ADDR ||        // 0x2C 总功率
        address == CF_SUM_ADDR ||          // 0x39 总脉冲计数
        address == FREQUENCY_ADDR ||       // 0x4E 频率
        address == TEMPERATURE_ADDR ||     // 0x5E 温度
        address == MODE2_ADDR) {           // 0x97 工作模式寄存器2
        return true;
    }
    
    // 检查数据寄存器
    const chip_params_internal_t* p = &CHIP_PARAMS[(uint8_t)chip];
    for (int ch = 0; ch < p->channel_count; ch++) {
        if (address == p->i_rms_addr[ch] ||
            address == p->watt_addr[ch] ||
            address == p->cf_cnt_addr[ch]) {
            return true;
        }
    }
    
    return false;
}

// 寄存器类型检查函数
static bool is_16bit_register_internal(uint8_t address) {
    // 16位寄存器列表（从原代码提取）
    return (address >= 0x6D && address <= 0x75) ||  // RMSGN寄存器
           (address >= 0x78 && address <= 0x80) ||  // RMSOS寄存器
           (address >= 0xA0 && address <= 0xB5) ||  // CHGN/CHOS寄存器
           (address >= 0xB6 && address <= 0xC9);    // WATTGN/WATTOS寄存器
}

static bool is_unsigned_register_internal(uint8_t address) {
    // 无符号寄存器列表（从原代码提取）
    return (address >= 0x0C && address <= 0x16) ||  // I_RMS, V_RMS
           (address >= 0x2F && address <= 0x39) ||  // CF_CNT, CF_SUM
           (address == FREQUENCY_ADDR) ||            // 频率
           (address == TEMPERATURE_ADDR);            // 温度
}

// ============================================================================
// 校准系数计算函数（从 bl0906_calibration.h 迁移）
// ============================================================================

// 编译时预计算的常用配置系数
struct PrecomputedCoefficients {
    float Ki, Kv, Kp, Ke, Kp_sum, Ke_sum;
    float FREF, TREF;
};

// 电压互感器采样方式 - 默认配置（编译时计算）
static constexpr PrecomputedCoefficients TRANSFORMER_DEFAULT_COEFFS = {
    .Ki = (12875.0f * Gain_I * (RL + RL) * 1000.0f / Rt) / Vref,
    .Kv = (13162.0f * Gain_V * R46 * 1000.0f) / (Vref * Rf_transformer),
    .Kp = 2.3847e-7f * ((12875.0f * Gain_I * (RL + RL) * 1000.0f / Rt) / Vref) * 
          ((13162.0f * Gain_V * R46 * 1000.0f) / (Vref * Rf_transformer)),
    .Ke = (3600000.0f * 16.0f * 2.3847e-7f * ((12875.0f * Gain_I * (RL + RL) * 1000.0f / Rt) / Vref) * 
          ((13162.0f * Gain_V * R46 * 1000.0f) / (Vref * Rf_transformer))) / (4194304.0f * 0.032768f * 16.0f),
    .Kp_sum = (2.3847e-7f * ((12875.0f * Gain_I * (RL + RL) * 1000.0f / Rt) / Vref) * 
              ((13162.0f * Gain_V * R46 * 1000.0f) / (Vref * Rf_transformer))) / 16.0f,
    .Ke_sum = ((3600000.0f * 16.0f * 2.3847e-7f * ((12875.0f * Gain_I * (RL + RL) * 1000.0f / Rt) / Vref) * 
              ((13162.0f * Gain_V * R46 * 1000.0f) / (Vref * Rf_transformer))) / (4194304.0f * 0.032768f * 16.0f)) / 16.0f,
    .FREF = FREF,
    .TREF = TREF
};

// 电阻分压采样方式 - 默认配置（编译时计算）
static constexpr PrecomputedCoefficients DIVIDER_DEFAULT_COEFFS = {
    .Ki = 12875.0f * Gain_I * 2.0f * RL * 1000.0f / Rt / Vref,
    .Kv = 13162.0f * Rv_divider * 1000.0f * Gain_V / (Vref * (Rf_divider + Rv_divider)),
    .Kp = (40.4125f * Gain_V * Gain_I * RL * 2.0f * 1000.0f / Rt) * Rv_divider * 1000.0f / (Vref * Vref * (Rf_divider + Rv_divider)),
    .Ke = (3600000.0f * 16.0f * (40.4125f * Gain_V * Gain_I * RL * 2.0f * 1000.0f / Rt) * Rv_divider * 1000.0f / (Vref * Vref * (Rf_divider + Rv_divider))) / (4194304.0f * 0.032768f * 16.0f),
    .Kp_sum = ((40.4125f * Gain_V * Gain_I * RL * 2.0f * 1000.0f / Rt) * Rv_divider * 1000.0f / (Vref * Vref * (Rf_divider + Rv_divider))) / 16.0f,
    .Ke_sum = ((3600000.0f * 16.0f * (40.4125f * Gain_V * Gain_I * RL * 2.0f * 1000.0f / Rt) * Rv_divider * 1000.0f / (Vref * Vref * (Rf_divider + Rv_divider))) / (4194304.0f * 0.032768f * 16.0f)) / 16.0f,
    .FREF = FREF,
    .TREF = TREF
};

// 检查参数是否为默认值
static bool is_default_transformer_params(const bl0906_reference_params_t* ref_params) {
    return (ref_params->Vref == Vref) &&
           (ref_params->Gain_V == Gain_V) &&
           (ref_params->Gain_I == Gain_I) &&
           (ref_params->RL == RL) &&
           (ref_params->Rt == Rt) &&
           (ref_params->Rf == Rf_transformer || ref_params->Rf == 0) &&
           (ref_params->R46 == R46 || ref_params->R46 == 0);
}

static bool is_default_divider_params(const bl0906_reference_params_t* ref_params) {
    return (ref_params->Vref == Vref) &&
           (ref_params->Gain_V == Gain_V) &&
           (ref_params->Gain_I == Gain_I) &&
           (ref_params->RL == RL) &&
           (ref_params->Rt == Rt) &&
           (ref_params->Rf == Rf_divider || ref_params->Rf == 0) &&
           (ref_params->Rv == Rv_divider || ref_params->Rv == 0);
}

// 优化的系数计算函数
static void calculate_calibration_coefficients_internal(bl0906_voltage_sampling_mode_t sampling_mode,
                                                        const bl0906_reference_params_t* ref_params,
                                                        bl0906_calibration_coefficients_t* coefficients) {
    // 优先使用编译时预计算的系数
    if (sampling_mode == BL0906_VOLTAGE_SAMPLING_TRANSFORMER) {
        if (is_default_transformer_params(ref_params)) {
            // 使用编译时预计算的系数（最高性能）
            coefficients->Ki = TRANSFORMER_DEFAULT_COEFFS.Ki;
            coefficients->Kv = TRANSFORMER_DEFAULT_COEFFS.Kv;
            coefficients->Kp = TRANSFORMER_DEFAULT_COEFFS.Kp;
            coefficients->Ke = TRANSFORMER_DEFAULT_COEFFS.Ke;
            coefficients->Kp_sum = TRANSFORMER_DEFAULT_COEFFS.Kp_sum;
            coefficients->Ke_sum = TRANSFORMER_DEFAULT_COEFFS.Ke_sum;
            coefficients->FREF = TRANSFORMER_DEFAULT_COEFFS.FREF;
            coefficients->TREF = TRANSFORMER_DEFAULT_COEFFS.TREF;
            return;
        }
        
        // 非默认参数，运行时计算
        float rf = ref_params->Rf > 0 ? ref_params->Rf : Rf_transformer;
        float r46 = ref_params->R46 > 0 ? ref_params->R46 : R46;
        
        coefficients->Ki = (12875.0f * ref_params->Gain_I * (ref_params->RL + ref_params->RL) * 1000.0f / ref_params->Rt) / ref_params->Vref;
        coefficients->Kv = (13162.0f * ref_params->Gain_V * r46 * 1000.0f) / (ref_params->Vref * rf);
        coefficients->Kp = 2.3847e-7f * coefficients->Ki * coefficients->Kv;
        coefficients->Ke = (3600000.0f * 16.0f * coefficients->Kp) / (4194304.0f * 0.032768f * 16.0f);
    } else {
        if (is_default_divider_params(ref_params)) {
            // 使用编译时预计算的系数（最高性能）
            coefficients->Ki = DIVIDER_DEFAULT_COEFFS.Ki;
            coefficients->Kv = DIVIDER_DEFAULT_COEFFS.Kv;
            coefficients->Kp = DIVIDER_DEFAULT_COEFFS.Kp;
            coefficients->Ke = DIVIDER_DEFAULT_COEFFS.Ke;
            coefficients->Kp_sum = DIVIDER_DEFAULT_COEFFS.Kp_sum;
            coefficients->Ke_sum = DIVIDER_DEFAULT_COEFFS.Ke_sum;
            coefficients->FREF = DIVIDER_DEFAULT_COEFFS.FREF;
            coefficients->TREF = DIVIDER_DEFAULT_COEFFS.TREF;
            return;
        }
        
        // 非默认参数，运行时计算
        float rf = ref_params->Rf > 0 ? ref_params->Rf : Rf_divider;
        float rv = ref_params->Rv > 0 ? ref_params->Rv : Rv_divider;
        
        coefficients->Ki = 12875.0f * ref_params->Gain_I * 2.0f * ref_params->RL * 1000.0f / ref_params->Rt / ref_params->Vref;
        coefficients->Kv = 13162.0f * rv * 1000.0f * ref_params->Gain_V / (ref_params->Vref * (rf + rv));
        coefficients->Kp = (40.4125f * ref_params->Gain_V * ref_params->Gain_I * ref_params->RL * 2.0f * 1000.0f / ref_params->Rt) * rv * 1000.0f / (ref_params->Vref * ref_params->Vref * (rf + rv));
        coefficients->Ke = (3600000.0f * 16.0f * coefficients->Kp) / (4194304.0f * 0.032768f * 16.0f);
    }
    
    // 通用系数
    coefficients->Kp_sum = coefficients->Kp / 16.0f;
    coefficients->Ke_sum = coefficients->Ke / 16.0f;
    coefficients->FREF = FREF;
    coefficients->TREF = TREF;
}

// ============================================================================
// 原有的核心算法实现（保持不变）
// ============================================================================

// 数据转换算法（从 convert_raw_to_value 提取）
static float convert_raw_to_value_internal(uint8_t address, int32_t raw_value) {
    // 基础传感器寄存器检查
    if (address == FREQUENCY_ADDR) {
        // 频率是无符号数，确保使用无符号计算
        return (raw_value > 0) ? 10000000.0f / (uint32_t)raw_value : 0;
    }
    
    if (address == TEMPERATURE_ADDR) {
        // 温度是无符号数
        uint32_t unsigned_raw = (uint32_t)raw_value;
        return (unsigned_raw - 64) * 12.5f / 59.0f - 40.0f;
    }
    
    // 使用默认校准系数进行转换
    bl0906_calibration_coefficients_t coeffs;
    bl0906_reference_params_t default_params = {
        .Vref = Vref,
        .Gain_V = Gain_V,
        .Gain_I = Gain_I,
        .RL = RL,
        .Rt = Rt,
        .Rf = Rf_transformer,
        .R46 = R46,
        .Rv = 0
    };
    calculate_calibration_coefficients_internal(BL0906_VOLTAGE_SAMPLING_TRANSFORMER, &default_params, &coeffs);
    
    if (address == V_RMS_ADDR) {
        // 电压是无符号数
        return (uint32_t)raw_value / coeffs.Kv;
    }
    
    if (address == WATT_SUM_ADDR) {
        return raw_value / coeffs.Kp_sum;  // 保持有符号
    }
    
    if (address == CF_SUM_ADDR) {
        return (uint32_t)raw_value / coeffs.Ke_sum;
    }
    
    // 动态检查通道寄存器
    const chip_params_internal_t* params = &CHIP_PARAMS[g_chip_model];
    for (int i = 0; i < params->channel_count; i++) {
        if (address == params->i_rms_addr[i]) { // I_RMS
            return (uint32_t)raw_value / coeffs.Ki;
        }
        if (address == params->watt_addr[i]) { // WATT
            return raw_value / coeffs.Kp;  // 保持有符号
        }
        if (address == params->cf_cnt_addr[i]) { // CF_CNT
            return (uint32_t)raw_value / coeffs.Ke;
        }
    }
    
    // 检查MODE2寄存器
    if (address == MODE2_ADDR) {
        return (uint32_t)raw_value;
    }
    
    return raw_value;  // 未知寄存器直接返回原始值
}

// 写保护解除算法（从 turn_off_write_protect 提取）
static bl0906_result_t disable_write_protection_internal(void) {
    if (!g_callbacks.read_register || !g_callbacks.send_raw_command) {
        return BL0906_ERROR_COMMUNICATION;
    }
    
    // 检查当前写保护状态
    int32_t raw_value;
    bl0906_result_t result = g_callbacks.read_register(WR_PROTECT_REG, &raw_value);
    if (result != BL0906_SUCCESS) {
        return result;
    }
    
    // 分析写保护状态
    uint8_t low_byte = raw_value & 0xFF;
    uint8_t mid_byte = (raw_value >> 8) & 0xFF;
    uint16_t reg_16bit = (mid_byte << 8) | low_byte;
    bool is_unlocked = (reg_16bit == 0x5555);
    
    if (is_unlocked) {
        return BL0906_SUCCESS;  // 已经解锁
    }
    
    // 重复尝试解锁，最多3次
    for (int attempt = 0; attempt < 3; attempt++) {
        uint8_t unlock_cmd[6];
        
        if (g_comm_type == BL0906_COMM_UART) {
            // UART写保护解除命令
            uint8_t uart_cmd[6] = {0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7};
            memcpy(unlock_cmd, uart_cmd, 6);
        } else if (g_comm_type == BL0906_COMM_SPI) {
            // SPI写保护解除命令
            uint8_t spi_cmd[6] = {0x81, 0x9E, 0x00, 0x55, 0x55, 0x00};
            uint8_t checksum = (spi_cmd[0] + spi_cmd[1] + spi_cmd[2] + 
                               spi_cmd[3] + spi_cmd[4]) ^ 0xFF;
            spi_cmd[5] = checksum;
            memcpy(unlock_cmd, spi_cmd, 6);
        } else {
            return BL0906_ERROR_INVALID_PARAM;
        }
        
        // 发送解锁命令
        result = g_callbacks.send_raw_command(unlock_cmd, 6);
        if (result != BL0906_SUCCESS) {
            continue;
        }
        
        // 验证写保护是否成功解除
        result = g_callbacks.read_register(WR_PROTECT_REG, &raw_value);
        if (result != BL0906_SUCCESS) {
            continue;
        }
        
        low_byte = raw_value & 0xFF;
        mid_byte = (raw_value >> 8) & 0xFF;
        reg_16bit = (mid_byte << 8) | low_byte;
        is_unlocked = (reg_16bit == 0x5555);
        
        if (is_unlocked) {
            return BL0906_SUCCESS;
        }
    }
    
    return BL0906_ERROR_WRITE_PROTECT;
}

// 频率模式设置算法
static bl0906_result_t set_frequency_mode_internal(bool is_60hz) {
    if (!g_callbacks.read_register || !g_callbacks.write_register) {
        return BL0906_ERROR_COMMUNICATION;
    }
    
    // 读取当前MODE2寄存器值
    int32_t current_mode2;
    bl0906_result_t result = g_callbacks.read_register(MODE2_ADDR, &current_mode2);
    if (result != BL0906_SUCCESS) {
        return result;
    }
    
    // 修改频率选择位
    uint32_t new_mode2 = (uint32_t)current_mode2;
    if (is_60hz) {
        new_mode2 |= MODE2_AC_FREQ_60HZ;   // 设置为60Hz
    } else {
        new_mode2 &= ~MODE2_AC_FREQ_SEL_MASK;  // 清除频率选择位，设置为50Hz
        new_mode2 |= MODE2_AC_FREQ_50HZ;
    }
    
    // 写入新的MODE2值（只写入低16位）
    int16_t mode2_16bit = (int16_t)(new_mode2 & 0xFFFF);
    result = g_callbacks.write_register(MODE2_ADDR, mode2_16bit);
    
    return result;
}

// ============================================================================
// 公共API实现
// ============================================================================

bl0906_result_t bl0906_core_init(bl0906_chip_model_t chip_model, 
                                bl0906_comm_type_t comm_type,
                                const bl0906_comm_callbacks_t* callbacks) {
    if (!callbacks || !callbacks->read_register || !callbacks->write_register) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    if (chip_model >= sizeof(CHIP_PARAMS) / sizeof(CHIP_PARAMS[0])) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    g_chip_model = chip_model;
    g_comm_type = comm_type;
    g_callbacks = *callbacks;
    g_initialized = true;
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_read_sensor_data(uint8_t channel, bl0906_sensor_data_t* data) {
    if (!g_initialized || !data) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    const chip_params_internal_t* params = &CHIP_PARAMS[g_chip_model];
    if (channel >= params->channel_count) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    // 初始化数据结构
    memset(data, 0, sizeof(bl0906_sensor_data_t));
    
    // 读取各种传感器数据
    int32_t raw_value;
    bl0906_result_t result;
    
    // 读取电压（全局）
    result = g_callbacks.read_register(V_RMS_ADDR, &raw_value);
    if (result == BL0906_SUCCESS) {
        data->voltage = convert_raw_to_value_internal(V_RMS_ADDR, raw_value);
    }
    
    // 读取频率（全局）
    result = g_callbacks.read_register(FREQUENCY_ADDR, &raw_value);
    if (result == BL0906_SUCCESS) {
        data->frequency = convert_raw_to_value_internal(FREQUENCY_ADDR, raw_value);
    }
    
    // 读取温度（全局）
    result = g_callbacks.read_register(TEMPERATURE_ADDR, &raw_value);
    if (result == BL0906_SUCCESS) {
        data->temperature = convert_raw_to_value_internal(TEMPERATURE_ADDR, raw_value);
    }
    
    // 读取通道特定数据
    if (channel < params->channel_count) {
        // 读取电流
        result = g_callbacks.read_register(params->i_rms_addr[channel], &raw_value);
        if (result == BL0906_SUCCESS) {
            data->current = convert_raw_to_value_internal(params->i_rms_addr[channel], raw_value);
        }
        
        // 读取功率
        result = g_callbacks.read_register(params->watt_addr[channel], &raw_value);
        if (result == BL0906_SUCCESS) {
            data->power = convert_raw_to_value_internal(params->watt_addr[channel], raw_value);
        }
        
        // 读取电量
        result = g_callbacks.read_register(params->cf_cnt_addr[channel], &raw_value);
        if (result == BL0906_SUCCESS) {
            data->energy = convert_raw_to_value_internal(params->cf_cnt_addr[channel], raw_value);
        }
    }
    
    data->valid = true;
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_convert_raw_to_value(uint8_t register_addr, 
                                                int32_t raw_value, 
                                                float* converted_value) {
    if (!g_initialized || !converted_value) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    *converted_value = convert_raw_to_value_internal(register_addr, raw_value);
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_apply_calibration(const bl0906_calibration_entry_t* entries, 
                                             size_t count) {
    if (!g_initialized || !entries || count == 0) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    // 首先解除写保护
    bl0906_result_t result = disable_write_protection_internal();
    if (result != BL0906_SUCCESS) {
        return result;
    }
    
    // 逐个应用校准条目
    for (size_t i = 0; i < count; i++) {
        result = g_callbacks.write_register(entries[i].register_addr, entries[i].value);
        if (result != BL0906_SUCCESS) {
            return result;
        }
    }
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_calculate_calibration(uint8_t register_addr,
                                                 int32_t raw_value,
                                                 float reference_value,
                                                 int16_t* calibration_coefficient) {
    if (!g_initialized || !calibration_coefficient || reference_value == 0) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    // 简化的校准系数计算
    float ratio = reference_value / convert_raw_to_value_internal(register_addr, raw_value);
    *calibration_coefficient = (int16_t)(ratio * 16384.0f);  // 假设16384为基准
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_disable_write_protection(void) {
    if (!g_initialized) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    return disable_write_protection_internal();
}

bl0906_result_t bl0906_core_set_frequency_mode(bool is_60hz) {
    if (!g_initialized) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    return set_frequency_mode_internal(is_60hz);
}

bl0906_result_t bl0906_core_detect_grid_frequency(float* frequency) {
    if (!g_initialized || !frequency) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    int32_t raw_value;
    bl0906_result_t result = g_callbacks.read_register(FREQUENCY_ADDR, &raw_value);
    if (result != BL0906_SUCCESS) {
        return result;
    }
    
    *frequency = convert_raw_to_value_internal(FREQUENCY_ADDR, raw_value);
    return BL0906_SUCCESS;
}

// ============================================================================
// 芯片参数相关API实现（新增）
// ============================================================================

bl0906_result_t bl0906_core_get_chip_info(bl0906_chip_model_t chip_model, 
                                          bl0906_chip_info_t* info) {
    if (!info) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    if (chip_model >= sizeof(CHIP_PARAMS) / sizeof(CHIP_PARAMS[0])) {
        info->is_valid = false;
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    const chip_params_internal_t* params = &CHIP_PARAMS[chip_model];
    info->max_channels = params->channel_count;
    info->chip_name = params->chip_name;
    info->is_valid = true;
    
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_get_register_address(bl0906_chip_model_t chip_model,
                                                 bl0906_register_type_t reg_type,
                                                 int channel,
                                                 uint8_t* address) {
    if (!address) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    if (chip_model >= sizeof(CHIP_PARAMS) / sizeof(CHIP_PARAMS[0])) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    uint8_t addr = get_register_addr_internal(chip_model, reg_type, channel);
    if (addr == 0) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    *address = addr;
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_validate_register_address(bl0906_chip_model_t chip_model,
                                                      uint8_t address,
                                                      bool* is_valid) {
    if (!is_valid) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    if (chip_model >= sizeof(CHIP_PARAMS) / sizeof(CHIP_PARAMS[0])) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    *is_valid = is_valid_register_for_chip_internal(chip_model, address);
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_is_16bit_register(uint8_t address, bool* is_16bit) {
    if (!is_16bit) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    *is_16bit = is_16bit_register_internal(address);
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_is_unsigned_register(uint8_t address, bool* is_unsigned) {
    if (!is_unsigned) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    *is_unsigned = is_unsigned_register_internal(address);
    return BL0906_SUCCESS;
}

// ============================================================================
// 校准系数相关API实现（新增）
// ============================================================================

bl0906_result_t bl0906_core_calculate_calibration_coefficients(bl0906_voltage_sampling_mode_t sampling_mode,
                                                              const bl0906_reference_params_t* ref_params,
                                                              bl0906_calibration_coefficients_t* coefficients) {
    if (!ref_params || !coefficients) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    if (ref_params->Vref <= 0 || ref_params->Rt <= 0 || ref_params->RL <= 0) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    calculate_calibration_coefficients_internal(sampling_mode, ref_params, coefficients);
    return BL0906_SUCCESS;
}

bl0906_result_t bl0906_core_get_default_reference_params(bl0906_voltage_sampling_mode_t sampling_mode,
                                                         bl0906_reference_params_t* ref_params) {
    if (!ref_params) {
        return BL0906_ERROR_INVALID_PARAM;
    }
    
    // 设置通用默认参数
    ref_params->Vref = Vref;
    ref_params->Gain_V = Gain_V;
    ref_params->Gain_I = Gain_I;
    ref_params->RL = RL;
    ref_params->Rt = Rt;
    
    if (sampling_mode == BL0906_VOLTAGE_SAMPLING_TRANSFORMER) {
        // 电压互感器采样方式
        ref_params->Rf = Rf_transformer;
        ref_params->R46 = R46;
        ref_params->Rv = 0;  // 不使用
    } else {
        // 电阻分压采样方式
        ref_params->Rf = Rf_divider;
        ref_params->R46 = 0;  // 不使用
        ref_params->Rv = Rv_divider;
    }
    
    return BL0906_SUCCESS;
}

// ============================================================================
// 工具函数
// ============================================================================

const char* bl0906_core_get_error_string(bl0906_result_t result) {
    switch (result) {
        case BL0906_SUCCESS: return "Success";
        case BL0906_ERROR_INVALID_PARAM: return "Invalid parameter";
        case BL0906_ERROR_COMMUNICATION: return "Communication error";
        case BL0906_ERROR_TIMEOUT: return "Timeout";
        case BL0906_ERROR_CHECKSUM: return "Checksum error";
        case BL0906_ERROR_WRITE_PROTECT: return "Write protection error";
        case BL0906_ERROR_UNKNOWN: return "Unknown error";
        default: return "Undefined error";
    }
}

const char* bl0906_core_get_version(void) {
    return "BL0906 Core Library v2.0.0 - Extended with Chip Parameters and Calibration";
}

void bl0906_core_deinit(void) {
    g_initialized = false;
    memset(&g_callbacks, 0, sizeof(g_callbacks));
} 