#!/bin/bash
# BL0906 Factory 发布版构建脚本
# 用途：构建用户友好的发布版本，移除校准调试功能

echo "=== BL0906 Factory 发布版构建 ==="

# 检查当前目录
if [ ! -f "__init__.py" ]; then
    echo "错误：未找到 __init__.py 文件，请在 bl0906_factory 组件目录中运行此脚本"
    exit 1
fi

# 确保使用发布版配置
echo "✓ 使用发布版配置（默认）"

# 构建预编译库
echo "构建预编译库..."
if [ -f "build_precompiled_lib.sh" ]; then
    chmod +x build_precompiled_lib.sh
    ./build_precompiled_lib.sh
    
    if [ $? -ne 0 ]; then
        echo "错误：预编译库构建失败"
        exit 1
    fi
    echo "✓ 预编译库构建成功"
else
    echo "警告：未找到预编译库构建脚本，跳过预编译库构建"
fi

# 设置构建环境变量
export BUILD_TYPE=release
export CPPFLAGS="-DBL0906_PRODUCTION_BUILD -DBL0906_READONLY_CALIBRATION -DBL0906_FAULT_TOLERANT"

echo "✓ 设置发布版编译标志："
echo "  - BL0906_PRODUCTION_BUILD: 发布版标识"
echo "  - BL0906_READONLY_CALIBRATION: 只读校准模式"
echo "  - BL0906_FAULT_TOLERANT: 容错运行模式"

# 验证发布版配置
if grep -q "number" __init__.py; then
    echo "警告：当前 __init__.py 可能包含 number 组件，请确认是否为发布版配置"
fi

if grep -q "AUTO_LOAD.*sensor.*number" __init__.py; then
    echo "错误：发布版不应包含 number 组件"
    exit 1
fi

echo "✓ 发布版配置验证通过"

# 检查必要文件
required_files=(
    "bl0906_factory.cpp"
    "bl0906_factory.h"
    "sensor.py"
    "config_mappings.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误：缺少必要文件 $file"
        exit 1
    fi
done

echo "✓ 必要文件检查完成"

# 构建信息
echo ""
echo "=== 发布版特性 ==="
echo "✓ 移除 number 组件（校准调试功能）"
echo "✓ 容错运行模式（校准数据缺失时继续工作）"
echo "✓ 只读校准数据（从存储读取，不可修改）"
echo "✓ 简化配置（用户友好）"
echo "✓ 算法保护（核心功能通过预编译库保护）"

echo ""
echo "=== 发布版构建完成 ==="
echo "当前配置为发布版，适用于最终用户部署"
echo "如需切换到生产版，请运行: ./build_production.sh"
echo "" 