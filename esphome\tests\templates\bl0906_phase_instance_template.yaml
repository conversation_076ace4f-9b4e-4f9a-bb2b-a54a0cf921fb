# BL0906相位实例模板
# 用于生成单个相位的完整BL0906Factory配置
# 使用方法:
# packages:
#   phase_a: !include
#     file: templates/bl0906_phase_instance_template.yaml
#     vars:
#       phase_name: "A"
#       phase_lower: "a"
#       instance_id: "0x906B0001"
#       uart_id: "uart_bus_a"
#       ch1_name: "${chA1}"
#       ch2_name: "${chA2}"
#       ch3_name: "${chA3}"
#       ch4_name: "${chA4}"
#       ch5_name: "${chA5}"
#       ch6_name: "${chA6}"
#       current_group: "currentA"
#       power_group: "powerA"
#       energy_group: "energyA"

defaults:
  phase_name: "A"
  phase_lower: "a"
  instance_id: "0x906B0001"
  uart_id: "uart_bus_a"
  ch1_name: "ch_A1"
  ch2_name: "ch_A2"
  ch3_name: "ch_A3"
  ch4_name: "ch_A4"
  ch5_name: "ch_A5"
  ch6_name: "ch_A6"
  current_group: "currentA"
  power_group: "powerA"
  energy_group: "energyA"

bl0906_factory:
  - id: sensor_bl0906_${phase_lower}
    communication: uart
    voltage_sampling_mode: resistor_divider
    uart_id: ${uart_id}
    update_interval: 1s
    instance_id: ${instance_id}
    calibration_mode: true
    chgn_reference_sensor: chgn_reference_value_${phase_lower}
    calibration:
      enabled: true
      storage_type: preference

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_${phase_lower}
    
    frequency:
      name: '${phase_name} Phase Frequency'
      icon: "mdi:sine-wave"
      web_server:
        sorting_group_id: power_status 
        
    temperature:
      name: '${phase_name} Phase BL0906 Temperature'
      icon: "mdi:thermometer"
      web_server:
          sorting_group_id: miscellaneous
          
    voltage:
      name: '${phase_name} Phase Voltage'
      icon: "mdi:lightning-bolt"
      id: ${phase_name}_voltage
      web_server:
        sorting_group_id: voltage_status 
      filters: 
        - sliding_window_moving_average: 
            window_size: 10
            send_every: 1
            send_first_at: 1
        
    power_sum:
      name: "${phase_name} Phase sum power"
      icon: "mdi:sigma"
      web_server:
        sorting_group_id: ${power_group}
        
    energy_sum: 
      id: chs${phase_name}_energy_sum
      name: "${phase_name} phase sum energy"
    
    # 通道1配置
    ch1:
      current:
        name: "${ch1_name} current"
        icon: "mdi:current-ac"
        id: ch${phase_name}1_current
        web_server:
          sorting_group_id: ${current_group}
        filters: 
          - sliding_window_moving_average: 
              window_size: 10
              send_every: 1
              send_first_at: 1
      power:
        name: "${ch1_name} power"
        id: ch${phase_name}1_power
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: ${power_group}
      energy:
        name: "${ch1_name} energy"
        id: ch${phase_name}1_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: ${energy_group}
        accuracy_decimals: 3
        unit_of_measurement: kWh

    # 通道2配置
    ch2:
      current:
        name: "${ch2_name} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: ${current_group}
      power:
        name: "${ch2_name} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: ${power_group}
      energy:
        name: "${ch2_name} energy"
        id: ch${phase_name}2_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: ${energy_group}
        accuracy_decimals: 3
        unit_of_measurement: kWh

    # 通道3配置
    ch3:
      current:
        name: "${ch3_name} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: ${current_group}
      power:
        name: "${ch3_name} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: ${power_group}
      energy:
        name: "${ch3_name} energy"
        id: ch${phase_name}3_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: ${energy_group}
        accuracy_decimals: 3
        unit_of_measurement: kWh

    # 通道4配置
    ch4:
      current:
        name: "${ch4_name} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: ${current_group}
      power:
        name: "${ch4_name} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: ${power_group}
      energy:
        name: "${ch4_name} energy"
        id: ch${phase_name}4_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: ${energy_group}
        accuracy_decimals: 3
        unit_of_measurement: kWh

    # 通道5配置
    ch5:
      current:
        name: "${ch5_name} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: ${current_group}
      power:
        name: "${ch5_name} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: ${power_group}
      energy:
        name: "${ch5_name} energy"
        id: ch${phase_name}5_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: ${energy_group}
        accuracy_decimals: 3
        unit_of_measurement: kWh

    # 通道6配置
    ch6:
      current:
        name: "${ch6_name} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: ${current_group}
      power:
        name: "${ch6_name} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: ${power_group}
      energy:
        name: "${ch6_name} energy"
        id: ch${phase_name}6_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: ${energy_group}
        accuracy_decimals: 3
        unit_of_measurement: kWh 