#pragma once
#include <cstdint>

namespace esphome {
namespace bl0906_factory {

// ========== 芯片型号枚举 ==========
enum class ChipModel : uint8_t {
  BL0906 = 0,
  BL0910 = 1
};

// ========== 校准寄存器类型枚举 ==========
enum class CalibRegType {
  I_RMS,    // 电流有效值
  WATT,     // 功率
  CF_CNT,   // 脉冲计数
  RMSGN,    // 有效值增益
  RMSOS,    // 有效值偏置
  CHGN,     // 电流增益
  CHOS,     // 电流偏置
  WATTGN,   // 功率增益
  WATTOS,   // 功率偏置
  CHGN_V,   // 电压增益
  CHOS_V    // 电压偏置
};

// ========== 紧凑的芯片参数结构 ==========
struct ChipParams {
  uint8_t channel_count;
  const uint8_t* i_rms_addr;
  const uint8_t* watt_addr;
  const uint8_t* cf_cnt_addr;
  const uint8_t* rmsgn_addr;
  const uint8_t* rmsos_addr;
  const uint8_t* chgn_addr;
  const uint8_t* chos_addr;
  const uint8_t* wattgn_addr;
  const uint8_t* wattos_addr;
  const char* chip_name;
};

// ========== 预定义地址数组 ==========
// BL0906 地址数组
static constexpr uint8_t BL0906_I_RMS_ADDRS[] = {0x0D, 0x0E, 0x0F, 0x10, 0x13, 0x14};
static constexpr uint8_t BL0906_WATT_ADDRS[] = {0x23, 0x24, 0x25, 0x26, 0x29, 0x2A};
static constexpr uint8_t BL0906_CF_CNT_ADDRS[] = {0x30, 0x31, 0x32, 0x33, 0x36, 0x37};
static constexpr uint8_t BL0906_RMSGN_ADDRS[] = {0x6D, 0x6E, 0x6F, 0x70, 0x73, 0x74};
static constexpr uint8_t BL0906_RMSOS_ADDRS[] = {0x78, 0x79, 0x7A, 0x7B, 0x7E, 0x7F};
static constexpr uint8_t BL0906_CHGN_ADDRS[] = {0xA1, 0xA2, 0xA3, 0xA4, 0xA7, 0xA8};
static constexpr uint8_t BL0906_CHOS_ADDRS[] = {0xAC, 0xAD, 0xAE, 0xAF, 0xB2, 0xB3};
static constexpr uint8_t BL0906_WATTGN_ADDRS[] = {0xB7, 0xB8, 0xB9, 0xBA, 0xBD, 0xBE};
static constexpr uint8_t BL0906_WATTOS_ADDRS[] = {0xC1, 0xC2, 0xC3, 0xC4, 0xC7, 0xC8};

// BL0910 地址数组
static constexpr uint8_t BL0910_I_RMS_ADDRS[] = {0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15};
static constexpr uint8_t BL0910_WATT_ADDRS[] = {0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B};
static constexpr uint8_t BL0910_CF_CNT_ADDRS[] = {0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38};
static constexpr uint8_t BL0910_RMSGN_ADDRS[] = {0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75};
static constexpr uint8_t BL0910_RMSOS_ADDRS[] = {0x77, 0x78, 0x79, 0x7A, 0x7B, 0x7C, 0x7D, 0x7E, 0x7F, 0x80};
static constexpr uint8_t BL0910_CHGN_ADDRS[] = {0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9};
static constexpr uint8_t BL0910_CHOS_ADDRS[] = {0xAC, 0xAD, 0xAE, 0xAF, 0xB0, 0xB1, 0xB2, 0xB3, 0xB4, 0xB5};
static constexpr uint8_t BL0910_WATTGN_ADDRS[] = {0xB6, 0xB7, 0xB8, 0xB9, 0xBA, 0xBB, 0xBC, 0xBD, 0xBE, 0xBF};
static constexpr uint8_t BL0910_WATTOS_ADDRS[] = {0xC0, 0xC1, 0xC2, 0xC3, 0xC4, 0xC5, 0xC6, 0xC7, 0xC8, 0xC9};

// ========== 芯片参数查找表 ==========
static constexpr ChipParams CHIP_PARAMS[] = {
  // BL0906
  {6, BL0906_I_RMS_ADDRS, BL0906_WATT_ADDRS, BL0906_CF_CNT_ADDRS, BL0906_RMSGN_ADDRS, 
   BL0906_RMSOS_ADDRS, BL0906_CHGN_ADDRS, BL0906_CHOS_ADDRS, BL0906_WATTGN_ADDRS, 
   BL0906_WATTOS_ADDRS, "BL0906"},
  // BL0910
  {10, BL0910_I_RMS_ADDRS, BL0910_WATT_ADDRS, BL0910_CF_CNT_ADDRS, BL0910_RMSGN_ADDRS,
   BL0910_RMSOS_ADDRS, BL0910_CHGN_ADDRS, BL0910_CHOS_ADDRS, BL0910_WATTGN_ADDRS,
   BL0910_WATTOS_ADDRS, "BL0910"}
};

// ========== 高性能内联地址查找函数 ==========
inline uint8_t get_register_addr(ChipModel chip, CalibRegType type, int channel) {
  const ChipParams& p = CHIP_PARAMS[static_cast<uint8_t>(chip)];

  // 处理电压通道的特殊情况（channel = -1）
  if (channel == -1) {
    switch(type) {
      case CalibRegType::CHGN:   return 0xAA; // 电压通道增益（CHGN类型映射到CHGN_V寄存器）
      case CalibRegType::CHOS:   return 0xB5; // 电压通道偏置（CHOS类型映射到CHOS_V寄存器）
      case CalibRegType::CHGN_V: return 0xAA; // 电压通道增益（两芯片相同）
      case CalibRegType::CHOS_V: return 0xB5; // 电压通道偏置（两芯片相同）
      default: return 0; // 其他类型不支持电压通道
    }
  }

  // 处理普通通道
  if (channel < 0 || channel >= p.channel_count) return 0;

  switch(type) {
    case CalibRegType::I_RMS:  return p.i_rms_addr[channel];
    case CalibRegType::WATT:   return p.watt_addr[channel];
    case CalibRegType::CF_CNT: return p.cf_cnt_addr[channel];
    case CalibRegType::RMSGN:  return p.rmsgn_addr[channel];
    case CalibRegType::RMSOS:  return p.rmsos_addr[channel];
    case CalibRegType::CHGN:   return p.chgn_addr[channel];
    case CalibRegType::CHOS:   return p.chos_addr[channel];
    case CalibRegType::WATTGN: return p.wattgn_addr[channel];
    case CalibRegType::WATTOS: return p.wattos_addr[channel];
    case CalibRegType::CHGN_V: return 0xAA; // 电压通道增益（两芯片相同）
    case CalibRegType::CHOS_V: return 0xB5; // 电压通道偏置（两芯片相同）
    default: return 0;
  }
}

// ========== 通用寄存器地址常量 ==========
static constexpr uint8_t V_RMS_ADDR = 0x16;       // 电压有效值
static constexpr uint8_t FREQUENCY_ADDR = 0x4E;   // 频率
static constexpr uint8_t TEMPERATURE_ADDR = 0x5E; // 温度
static constexpr uint8_t WATT_SUM_ADDR = 0x2C;    // 总功率
static constexpr uint8_t CF_SUM_ADDR = 0x39;      // 总脉冲计数
static constexpr uint8_t MODE2_ADDR = 0x97;       // 工作模式寄存器2
static constexpr uint8_t CHGN_V_ADDR = 0xAA;      // 电压通道增益寄存器

// ========== 操作命令定义 ==========
static constexpr uint8_t UART_READ_COMMAND = 0x35;
static constexpr uint8_t UART_WRITE_COMMAND = 0xCA;
static constexpr uint8_t SPI_READ_COMMAND = 0x82;
static constexpr uint8_t SPI_WRITE_COMMAND = 0x81;

// ========== MODE2寄存器位掩码 ==========
static constexpr uint32_t MODE2_AC_FREQ_SEL_MASK = 0x800000;
static constexpr uint32_t MODE2_AC_FREQ_50HZ = 0x000000;
static constexpr uint32_t MODE2_AC_FREQ_60HZ = 0x800000;

// ========== 写保护控制指令 ==========
static constexpr uint8_t USR_WRPROT_ENABLE = 0x55;
static constexpr uint8_t USR_WRPROT_DISABLE = 0x00;

// ========== 寄存器地址验证函数 ==========
inline bool is_valid_calibration_register(ChipModel chip, uint8_t address) {
  const ChipParams& p = CHIP_PARAMS[static_cast<uint8_t>(chip)];
  
  // 检查电压校准寄存器（两芯片通用）
  if (address == 0xAA || address == 0xB5) {
    return true;
  }
  
  // 检查各类型校准寄存器
  for (int ch = 0; ch < p.channel_count; ch++) {
    if (address == p.rmsgn_addr[ch] ||
        address == p.rmsos_addr[ch] ||
        address == p.chgn_addr[ch] ||
        address == p.chos_addr[ch] ||
        address == p.wattgn_addr[ch] ||
        address == p.wattos_addr[ch]) {
      return true;
    }
  }
  
  return false;
}

inline bool is_valid_register_for_chip(ChipModel chip, uint8_t address) {
  // 首先检查是否为校准寄存器
  if (is_valid_calibration_register(chip, address)) {
    return true;
  }
  
  // 检查通用寄存器（两芯片都支持）
  if (address == V_RMS_ADDR ||           // 0x16 电压有效值
      address == WATT_SUM_ADDR ||        // 0x2C 总功率
      address == CF_SUM_ADDR ||          // 0x39 总脉冲计数
      address == FREQUENCY_ADDR ||       // 0x4E 频率
      address == TEMPERATURE_ADDR ||     // 0x5E 温度
      address == MODE2_ADDR) {           // 0x97 工作模式寄存器2
    return true;
  }
  
  // 检查数据寄存器
  const ChipParams& p = CHIP_PARAMS[static_cast<uint8_t>(chip)];
  for (int ch = 0; ch < p.channel_count; ch++) {
    if (address == p.i_rms_addr[ch] ||
        address == p.watt_addr[ch] ||
        address == p.cf_cnt_addr[ch]) {
      return true;
    }
  }
  
  return false;
}

// ========== 寄存器类型检查函数 ==========
inline CalibRegType get_register_type_by_address(ChipModel chip, uint8_t address) {
  const ChipParams& p = CHIP_PARAMS[static_cast<uint8_t>(chip)];
  
  // 检查电压校准寄存器
  if (address == 0xAA) return CalibRegType::CHGN_V;
  if (address == 0xB5) return CalibRegType::CHOS_V;
  
  // 检查各类型校准寄存器
  for (int ch = 0; ch < p.channel_count; ch++) {
    if (address == p.rmsgn_addr[ch]) return CalibRegType::RMSGN;
    if (address == p.rmsos_addr[ch]) return CalibRegType::RMSOS;
    if (address == p.chgn_addr[ch]) return CalibRegType::CHGN;
    if (address == p.chos_addr[ch]) return CalibRegType::CHOS;
    if (address == p.wattgn_addr[ch]) return CalibRegType::WATTGN;
    if (address == p.wattos_addr[ch]) return CalibRegType::WATTOS;
  }
  
  // 如果不是校准寄存器，返回一个默认值
  return CalibRegType::CHGN;  // 默认值，实际使用时应检查is_valid_calibration_register
}

// ========== 实用内联函数 ==========
inline bool is_16bit_register(uint8_t address) {
  // 校准寄存器都是16位
  return (address >= 0x6C && address <= 0x80) ||  // RMSGN/RMSOS 范围
         (address >= 0xA0 && address <= 0xC9) ||  // CHGN/CHOS/WATTGN/WATTOS 范围
         (address == 0xAA) || (address == 0xB5);  // 电压校准寄存器
}

inline bool is_unsigned_register(uint8_t address) {
  return (address == FREQUENCY_ADDR) || (address == TEMPERATURE_ADDR) || 
         (address == V_RMS_ADDR) || (address == CF_SUM_ADDR);
}

inline bool is_24bit_register(uint8_t address) {
  return address == MODE2_ADDR;
}

// ========== 数据结构定义 ==========
struct ube24_t {
  uint8_t l, m, h;  // 无符号24位：低、中、高字节
};

struct sbe24_t {
  uint8_t l, m;
  int8_t h;  // 有符号高字节，支持符号扩展
};

struct DataPacket {
  uint8_t l, m, h, checksum;
};

}  // namespace bl0906_factory
}  // namespace esphome 