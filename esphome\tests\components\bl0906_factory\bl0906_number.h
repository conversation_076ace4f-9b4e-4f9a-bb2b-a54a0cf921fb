#pragma once

#include "esphome/core/component.h"
#include "esphome/components/number/number.h"
#include <cstdint>

namespace esphome {
namespace bl0906_factory {



// 前向声明
class BL0906Factory;

// BL0906Number类 - 用于处理BL0906校准寄存器的Number组件
class BL0906Number : public number::Number, public Component {
 public:
  void setup() override;
  void dump_config() override;
  void control(float value) override;
  void set_register_address(uint8_t address);
  void set_initial_value(int16_t value) {
    initial_value_ = value;
    has_initial_value_ = true;
  }
  uint8_t get_register_address() const { return register_address_; }
  void update_from_register();

  // 添加设置父实例指针的方法
  void set_parent(BL0906Factory *parent) { parent_ = parent; }

  // 添加检查父指针的方法
  bool has_parent() const { return parent_ != nullptr; }

 protected:
  uint8_t register_address_{0};
  int16_t initial_value_{0};
  bool has_initial_value_{false};
  // 添加指向父实例的指针
  BL0906Factory *parent_{nullptr};
};

}  // namespace bl0906_factory
}  // namespace esphome