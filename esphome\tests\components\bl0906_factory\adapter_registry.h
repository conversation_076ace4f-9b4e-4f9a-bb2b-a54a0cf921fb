#pragma once

#include "communication_adapter_interface.h"
#include <memory>

namespace esphome {
namespace bl0906_factory {

/**
 * 适配器工厂类
 * 
 * 提供编译时适配器创建工厂，根据编译宏决定包含哪个适配器
 */
class CommunicationAdapterFactory {
public:
  /**
   * 创建通信适配器实例
   * 根据编译时宏定义创建相应的适配器
   * @return 适配器实例的智能指针
   */
  static std::unique_ptr<CommunicationAdapterInterface> create_adapter();

  /**
   * 创建UART适配器实例
   * @return UART适配器实例的智能指针
   */
  static std::unique_ptr<CommunicationAdapterInterface> create_uart_adapter();

  /**
   * 创建SPI适配器实例
   * @return SPI适配器实例的智能指针
   */
  static std::unique_ptr<CommunicationAdapterInterface> create_spi_adapter();

private:
  // 禁止实例化
  CommunicationAdapterFactory() = delete;
  ~CommunicationAdapterFactory() = delete;
  CommunicationAdapterFactory(const CommunicationAdapterFactory&) = delete;
  CommunicationAdapterFactory& operator=(const CommunicationAdapterFactory&) = delete;
};

} // namespace bl0906_factory
} // namespace esphome 