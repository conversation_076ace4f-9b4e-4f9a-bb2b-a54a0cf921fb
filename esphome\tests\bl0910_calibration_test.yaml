esphome:
  name: bl0910-calibration-test
  platform: ESP32
  board: esp32dev

# 配置串口用于日志输出
logger:
  level: DEBUG

# Wi-Fi配置
wifi:
  ssid: "TestSSID"
  password: "TestPassword"

# 启用Home Assistant API
api:

# 启用OTA
ota:

# 启用preferences组件用于数据持久化
preferences:
  flash_write_interval: 1min

# 定义UART组件
uart:
  id: uart_bus
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 9600

# BL0910 Factory配置 - 启用校准模式
bl0906_factory:
  chip_model: bl0910  # 关键配置：指定使用BL0910芯片
  communication: uart
  uart_id: uart_bus
  instance_id: 0x91000001  # BL0910专用实例ID
  update_interval: 30s
  calibration_mode: true   # 启用校准模式
  freq_adapt: auto
  voltage_sampling_mode: transformer
  
  # 校准配置
  calibration:
    storage_type: preference

# 传感器配置 - BL0910支持1-10通道
sensor:
  - platform: bl0906_factory
    voltage:
      name: "BL0910 Voltage"
    frequency:
      name: "BL0910 Frequency"
    temperature:
      name: "BL0910 Temperature"
    
    # 电流传感器 - 测试前4个通道
    current:
      - channel: 1
        name: "BL0910 Current Ch1"
      - channel: 2
        name: "BL0910 Current Ch2"
      - channel: 3
        name: "BL0910 Current Ch3"
      - channel: 4
        name: "BL0910 Current Ch4"
    
    # 功率传感器 - 测试前4个通道
    power:
      - channel: 1
        name: "BL0910 Power Ch1"
      - channel: 2
        name: "BL0910 Power Ch2"
      - channel: 3
        name: "BL0910 Power Ch3"
      - channel: 4
        name: "BL0910 Power Ch4"
    
    # 总和传感器
    power_sum:
      name: "BL0910 Total Power"

# 校准数字组件 - 测试BL0910的CHGN寄存器
number:
  - platform: bl0906_factory
    # 各通道电流增益校准 - 测试前4个通道
    chgn:
      - channel: 1
        name: "BL0910 Current Gain Ch1"
        id: chgn_ch1
      - channel: 2
        name: "BL0910 Current Gain Ch2"
        id: chgn_ch2
      - channel: 3
        name: "BL0910 Current Gain Ch3"
        id: chgn_ch3
      - channel: 4
        name: "BL0910 Current Gain Ch4"
        id: chgn_ch4
    
    # 电压增益校准
    chgn_v:
      name: "BL0910 Voltage Gain"
      id: chgn_v

# 测试按钮 - 用于校准数据操作
button:
  - platform: template
    name: "BL0910 Save Calibration"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("bl0910_test", "开始保存BL0910校准数据...");
            id(bl0906_factory_id).save_all_calibration_to_flash();
            ESP_LOGI("bl0910_test", "BL0910校准数据保存完成");

  - platform: template
    name: "BL0910 Load Calibration"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("bl0910_test", "开始加载BL0910校准数据...");
            id(bl0906_factory_id).load_calibration_from_flash();
            ESP_LOGI("bl0910_test", "BL0910校准数据加载完成");

  - platform: template
    name: "BL0910 Show Calibration Data"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("bl0910_test", "显示BL0910校准数据...");
            id(bl0906_factory_id).read_and_display_calibration_data();

  - platform: template
    name: "BL0910 Diagnose NVS"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("bl0910_test", "诊断BL0910 NVS存储...");
            id(bl0906_factory_id).diagnose_nvs_storage();

  - platform: template
    name: "BL0910 Test CHGN Addresses"
    on_press:
      then:
        - lambda: |-
            ESP_LOGI("bl0910_test", "测试BL0910 CHGN寄存器地址...");
            // 测试前4个通道的CHGN寄存器地址
            // BL0910的CHGN地址应该是: 0xA0, 0xA1, 0xA2, 0xA3, ...
            auto factory = id(bl0906_factory_id);
            
            // 读取并显示CHGN寄存器值
            for (int ch = 1; ch <= 4; ch++) {
                int32_t value = factory.read_register_value(0xA0 + ch - 1);  // 0xA0-0xA3
                ESP_LOGI("bl0910_test", "CHGN[%d] (0x%02X) = %d", ch, 0xA0 + ch - 1, value);
            }
            
            // 读取电压通道CHGN
            int32_t v_value = factory.read_register_value(0xAA);
            ESP_LOGI("bl0910_test", "CHGN_V (0xAA) = %d", v_value);

# 诊断信息
text_sensor:
  - platform: template
    name: "BL0910 Chip Model"
    lambda: |-
      return {"BL0910"};
  - platform: template
    name: "BL0910 Max Channels"
    lambda: |-
      return {"10"};
  - platform: template
    name: "BL0910 CHGN Addresses"
    lambda: |-
      return {"0xA0-0xA9, 0xAA(V)"};
