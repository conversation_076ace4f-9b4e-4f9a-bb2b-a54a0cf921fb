#include "./BL6552.h"
#include "esphome/core/log.h"
#include "esphome/core/hal.h"
#include "esphome/core/application.h"

namespace esphome {
namespace BL6552 {

const char *TAG = "BL6552";

// 校验和计算函数
uint8_t BL6552_checksum(const uint8_t address, const DataPacket *data) {
  uint8_t sum = address;
  sum += data->l;
  sum += data->m;
  sum += data->h;
  return ~sum;
}

void BL6552::loop() {
  // 只在主动更新时执行
  if (!is_updating_) 
    return;

  if (this->current_state_ >= 5) {
    this->current_state_ = 0;
    is_updating_ = false;  // 结束更新周期
    return;
  }

  // 状态机处理不同测量值
  switch (this->current_state_) {
    case 0:  // 基础参数
      read_data_(BL6552_FREQUENCY, 10000000.0f, frequency_sensor_);
      read_data_(BL6552_TEMPERATURE, 12.5f/59.0f-40.0f, temperature_sensor_);
      break;

    case 1:  // A相参数
      read_data_(BL6552_VA_RMS, 1.097f*(20000+20000+20000+20000+20000)/(13162*1*100*1000), va_sensor_);
      read_data_(BL6552_IA_RMS, 1.097f/(12875*1*(5.1f+5.1f)*1000/2000), ia_sensor_);
      read_data_(BL6552_WATTA, 1.097f*1.097f*(20000+20000+20000+20000+20000)/(40.41259f*((5.1f+5.1f)*1000/2000)*1*100*1*1000), watta_sensor_);
      read_data_(BL6552_PFA, 1.0f/32768.0f, pfa_sensor_);  // 功率因数转换系数
      read_data_(BL6552_CFA_CNT, 4194304*0.032768*16/(3600000*16*(40.4125f*((5.1f+5.1f)*1000/2000)*1*100*1*1000/(1.097f*1.097f*(20000+20000+20000+20000+20000)))), energy_a_sensor_);
      break;

    case 2:  // B相参数
      read_data_(BL6552_VB_RMS, 1.097f*(20000+20000+20000+20000+20000)/(13162*1*100*1000), vb_sensor_);
      read_data_(BL6552_IB_RMS, 1.097f/(12875*1*(5.1f+5.1f)*1000/2000), ib_sensor_);
      read_data_(BL6552_WATTB, 1.097f*1.097f*(20000+20000+20000+20000+20000)/(40.41259f*((5.1f+5.1f)*1000/2000)*1*100*1*1000), wattb_sensor_);
      read_data_(BL6552_PFB, 1.0f/32768.0f, pfb_sensor_);  // 功率因数转换系数
      read_data_(BL6552_CFB_CNT, 4194304*0.032768*16/(3600000*16*(40.4125f*((5.1f+5.1f)*1000/2000)*1*100*1*1000/(1.097f*1.097f*(20000+20000+20000+20000+20000)))), energy_b_sensor_);
      break;

    case 3:  // C相参数
      read_data_(BL6552_VC_RMS, 1.097f*(20000+20000+20000+20000+20000)/(13162*1*100*1000), vc_sensor_);
      read_data_(BL6552_IC_RMS, 1.097f/(12875*1*(5.1f+5.1f)*1000/2000), ic_sensor_);
      read_data_(BL6552_WATTC, 1.097f*1.097f*(20000+20000+20000+20000+20000)/(40.41259f*((5.1f+5.1f)*1000/2000)*1*100*1*1000), wattc_sensor_);
      read_data_(BL6552_PFC, 1.0f/32768.0f, pfc_sensor_);  // 功率因数转换系数
      read_data_(BL6552_CFC_CNT, 4194304*0.032768*16/(3600000*16*(40.4125f*((5.1f+5.1f)*1000/2000)*1*100*1*1000/(1.097f*1.097f*(20000+20000+20000+20000+20000)))), energy_c_sensor_);
      break;

    case 4:  // 总测量值
      read_data_(BL6552_WATT_SUM, 1.097f*1.097f*(20000+20000+20000+20000+20000)/(40.41259f*((5.1f+5.1f)*1000/2000)*1*100*1*1000), total_power_sensor_);
      read_data_(BL6552_CF_SUM_CNT, 4194304*0.032768*16/(3600000*16*(40.4125f*((5.1f+5.1f)*1000/2000)*1*100*1*1000/(1.097f*1.097f*(20000+20000+20000+20000+20000)))), total_energy_sensor_);
      read_data_(BL6552_PF_SUM, 1.0f/32768.0f, total_pf_sensor_);  // 新增合相功率因数读取
      break;

    default:
      this->current_state_ = 0;
      break;
  }
  
  this->current_state_++;
  arch_feed_wdt();
}

void BL6552::read_data_(uint8_t address, float reference, sensor::Sensor *sensor) {
  const uint8_t max_retries = 3;
  uint8_t retry_count = 0;

  while (retry_count < max_retries) {
    if (!sensor) {
      ESP_LOGW(TAG, "尝试读取未初始化的传感器，地址:0x%02X", address);
      return;
    }

    // 发送读命令
    this->write_byte(BL6552_READ_COMMAND);
    this->write_byte(address);

    // 等待数据可用
    if (!wait_until_available(sizeof(DataPacket), 50)) {
      ESP_LOGW(TAG, "读取超时，重试 %d/3", retry_count + 1);
      stats_.timeout_errors++;
      retry_count++;
      delay(10);  // 短暂延时后重试
      continue;
    }

    // 读取数据包
    DataPacket buffer;
    if (!read_array_with_timeout((uint8_t *)&buffer, sizeof(buffer), 50)) {
      ESP_LOGE(TAG, "数据包不完整");
      return;
    }

    // 验证校验和
    const uint8_t expected_checksum = BL6552_checksum(address, &buffer);
    if (buffer.checksum != expected_checksum) {
      ESP_LOGE(TAG, "校验异常 计算:%02X 接收:%02X", expected_checksum, buffer.checksum);
      stats_.checksum_errors++;
      return;
    }

    // 数据处理
    const bool is_signed = (address == BL6552_WATTA || address == BL6552_WATTB || address == BL6552_WATTC || 
                           address == BL6552_WATT_SUM || address == BL6552_TEMPERATURE);
    float value = 0;
    int32_t raw_value = 0;  // 添加原始值变量

    if (is_signed) {
      raw_value = (buffer.h << 16) | (buffer.m << 8) | buffer.l;
      raw_value = (raw_value << 8) >> 8;
      value = raw_value;
    } else {
      ube24_t data_u24 = {buffer.l, buffer.m, buffer.h};
      raw_value = to_uint32_t(data_u24);  // 保存原始值
      value = raw_value * reference;
    }

    // 特殊处理
    if (address == BL6552_FREQUENCY) {
      raw_value = to_uint32_t({buffer.l, buffer.m, buffer.h});  // 更新原始值
      value = 10000000.0f / raw_value;
    } else if (address == BL6552_TEMPERATURE) {
      value = (value - 64) * 12.5f/59.0f - 40.0f;
    }

    // 修改调试日志，使用正确的变量名
    ESP_LOGD(TAG, "读取地址:0x%02X 原始值:%d 转换值:%.3f", address, raw_value, value);
    
    sensor->publish_state(value);
    return;  // 成功读取后返回
  }

  ESP_LOGE(TAG, "读取失败，地址:0x%02X 已重试%d次", address, max_retries);
}

void BL6552::bias_correction(uint8_t address, float measurements, float correction) {
  const float ki = 12875 * 1 * (5.1f + 5.1f) * 1000 / 2000 / 1.097f;
  const float i_rms0 = measurements * ki;
  const float i_rms = correction * ki;
  const int32_t value = (i_rms * i_rms - i_rms0 * i_rms0) / 256;

  DataPacket data;
  data.l = static_cast<uint8_t>(value & 0xFF);
  data.m = static_cast<uint8_t>((value >> 8) & 0xFF);
  data.h = static_cast<uint8_t>((value >> 16) & 0x7F);
  
  if (value < 0)
    data.h |= 0x80;
  
  data.checksum = BL6552_checksum(address, &data);

  this->write_array({BL6552_WRITE_COMMAND, address, data.l, data.m, data.h, data.checksum});
}

void BL6552::setup() {
  ESP_LOGI(TAG, "开始初始化BL6552...");
  
  // 清空缓冲区
  while (this->available()) {
    uint8_t dummy;
    this->read_byte(&dummy);  // 读取并丢弃数据
  }

  // 硬件握手
  for (int i = 0; i < 3; i++) {  // 最多尝试3次
    this->write_byte(0x55);
    if (wait_until_available(1, 100)) {
      uint8_t response;
      if (this->read_byte(&response)) {
        ESP_LOGI(TAG, "设备响应成功");
        break;
      }
    }
    if (i == 2) {
      ESP_LOGE(TAG, "设备初始化失败，请检查连接");
      return;
    }
    delay(100);
  }

  // 解除写保护
  this->write_array(BL6552_WRPROT_WRITABLE, sizeof(BL6552_WRPROT_WRITABLE));

  // 更新电流校准寄存器调用（使用新地址）
  // A相校准配置
  bias_correction(BL6552_IA_RMSOS, 0.009f, 0.0f);  // 原BL6552_RMSOS_A → 改为BL6552_IA_RMSOS
  bias_correction(BL6552_IA_RMSGN, 1.0f, 1.0f);    // 原BL6552_RMSGN_A → 改为BL6552_IA_RMSGN

  // B相校准配置
  bias_correction(BL6552_IB_RMSOS, 0.009f, 0.0f);  // 原BL6552_RMSOS_B → 改为BL6552_IB_RMSOS
  bias_correction(BL6552_IB_RMSGN, 1.0f, 1.0f);    // 原BL6552_RMSGN_B → 改为BL6552_IB_RMSGN

  // C相校准配置
  bias_correction(BL6552_IC_RMSOS, 0.009f, 0.0f);  // 原BL6552_RMSOS_C → 改为BL6552_IC_RMSOS
  bias_correction(BL6552_IC_RMSGN, 1.0f, 1.0f);    // 原BL6552_RMSGN_C → 改为BL6552_IC_RMSGN

  // 新增电压校准配置（如果需要）
  // bias_correction(BL6552_VA_RMSOS, ...);
  // bias_correction(BL6552_VA_RMSGN, ...);
  
  // 重新启用写保护
  this->write_array(BL6552_WRPROT_READONLY, sizeof(BL6552_WRPROT_READONLY));

  ESP_LOGI(TAG, "BL6552三相电能计量芯片初始化完成");
}

void BL6552::update() {
  // 开始新的更新周期
  is_updating_ = true;
  this->current_state_ = 0;
  this->loop();  // 启动状态机
}

uint32_t BL6552::to_uint32_t(ube24_t input) {
  return input.h << 16 | input.m << 8 | input.l;
}

int32_t BL6552::to_int32_t(sbe24_t input) {
  return input.h << 16 | input.m << 8 | input.l;
}

bool BL6552::read_array_with_timeout(uint8_t *data, size_t len, uint32_t timeout_ms) {
  const uint32_t start = millis();
  size_t received = 0;
  while (received < len && (millis() - start < timeout_ms + 50)) {
    if (this->read_byte(&data[received])) {
      received++;
    }
    yield();
  }
  return received == len;
}

bool BL6552::wait_until_available(size_t len, uint32_t timeout_ms) {
  const uint32_t start = millis();
  while (this->available() < len) {
    if (millis() - start > timeout_ms)
      return false;
    yield();
  }
  return true;
}

}  // namespace BL6552
}  // namespace esphome
 