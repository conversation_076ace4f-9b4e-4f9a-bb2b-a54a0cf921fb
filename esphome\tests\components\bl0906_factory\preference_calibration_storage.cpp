#include "preference_calibration_storage.h"
#include "esphome/core/application.h"

namespace esphome {
namespace bl0906_factory {

const char *const PreferenceCalibrationStorage::TAG = "bl0906.preference_storage";

PreferenceCalibrationStorage::PreferenceCalibrationStorage() {
}

bool PreferenceCalibrationStorage::init() {
    ESP_LOGD(TAG, "初始化preference校准存储");
    return load_instance_list();
}

// 实现基类的原始数据操作接口
StorageResult PreferenceCalibrationStorage::read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) {
    std::string key = get_preference_key(instance_id);

    // 读取数据长度
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t stored_size = 0;

    if (!size_pref.load(&stored_size) || stored_size == 0) {
        return StorageResult::INSTANCE_NOT_FOUND;
    }

    // 检查缓冲区大小
    if (buffer_size < stored_size) {
        buffer_size = stored_size;
        return StorageResult::INVALID_DATA;
    }

    // 读取数据
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t temp_data[512];

    if (!pref.load(&temp_data)) {
        return StorageResult::INSTANCE_NOT_FOUND;
    }

    // 复制数据
    memcpy(buffer, temp_data, stored_size);
    buffer_size = stored_size;

    return StorageResult::SUCCESS;
}

StorageResult PreferenceCalibrationStorage::write_raw_data(uint32_t instance_id, const uint8_t* buffer, size_t buffer_size) {
    if (buffer_size > 512) {
        return StorageResult::INVALID_DATA;
    }

    std::string key = get_preference_key(instance_id);

    // 准备数据缓冲区
    uint8_t data[512] = {0};
    memcpy(data, buffer, buffer_size);

    // 保存数据
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    if (!pref.save(&data)) {
        ESP_LOGE(TAG, "保存数据失败，实例: 0x%08X", instance_id);
        return StorageResult::IO_ERROR;
    }

    // 保存大小记录
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t size_to_save = static_cast<uint16_t>(buffer_size);
    if (!size_pref.save(&size_to_save)) {
        ESP_LOGE(TAG, "保存数据长度失败，实例: 0x%08X", instance_id);
        return StorageResult::IO_ERROR;
    }

    // 强制提交到Flash
    global_preferences->sync();
    delay(100);

    ESP_LOGD(TAG, "实例 0x%08X 数据写入成功 (%d 字节)", instance_id, buffer_size);

    // 更新实例列表
    add_to_instance_list(instance_list_, instance_id);
    save_instance_list();

    return StorageResult::SUCCESS;
}

StorageResult PreferenceCalibrationStorage::delete_raw_data(uint32_t instance_id) {
    std::string key = get_preference_key(instance_id);

    // 删除大小记录
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t zero_size = 0;
    size_pref.save(&zero_size);

    // 清空数据
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t empty_data[512] = {0};
    pref.save(&empty_data);

    // 强制提交到Flash
    global_preferences->sync();
    delay(100);

    // 从实例列表中移除
    remove_from_instance_list(instance_list_, instance_id);
    save_instance_list();

    ESP_LOGD(TAG, "删除实例 0x%08X 成功", instance_id);
    return StorageResult::SUCCESS;
}

bool PreferenceCalibrationStorage::verify() {
    // preference存储由ESP32 NVS保证数据完整性
    return true;
}

bool PreferenceCalibrationStorage::erase() {
    // 删除所有实例
    for (uint32_t instance_id : instance_list_) {
        delete_raw_data(instance_id);
    }
    instance_list_.clear();
    save_instance_list();
    
    ESP_LOGD(TAG, "清除所有校准数据");
    return true;
}

std::vector<uint32_t> PreferenceCalibrationStorage::get_instance_list() {
    return instance_list_;
}

// Private helper methods
std::string PreferenceCalibrationStorage::get_preference_key(uint32_t instance_id) {
    char key[32];
    snprintf(key, sizeof(key), "bl0906_cal_%08X", instance_id);
    return std::string(key);
}

bool PreferenceCalibrationStorage::save_instance_list() {
    auto pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_factory_instances"));

    uint32_t data[5] = {0};
    size_t count = std::min(instance_list_.size(), size_t(4));
    data[0] = count;

    for (size_t i = 0; i < count; i++) {
        data[i + 1] = instance_list_[i];
    }

    bool result = pref.save(&data);
    if (result) {
        global_preferences->sync();
        delay(50);
    }
    return result;
}

bool PreferenceCalibrationStorage::load_instance_list() {
    auto pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_factory_instances"));

    uint32_t data[5];
    if (!pref.load(&data)) {
        instance_list_.clear();
        scan_existing_instances();
        if (!instance_list_.empty()) {
            save_instance_list();
        }
        return true;
    }

    uint32_t count = data[0];
    if (count > 4) {
        count = 4;
    }

    instance_list_.clear();
    for (uint32_t i = 0; i < count; i++) {
        uint32_t instance_id = data[i + 1];
        if (verify_instance_exists(instance_id)) {
            instance_list_.push_back(instance_id);
        }
    }

    return true;
}

void PreferenceCalibrationStorage::scan_existing_instances() {
    uint32_t known_instances[] = {
        0x906B0001,  // BL0906 实例1
        0x910B0001,  // BL0910 实例1
        0x906B0002,  // BL0906 实例2
        0x910B0002   // BL0910 实例2
    };

    for (uint32_t instance_id : known_instances) {
        if (verify_instance_exists(instance_id)) {
            instance_list_.push_back(instance_id);
        }
        if (instance_list_.size() >= 4) {
            break;
        }
    }
}

bool PreferenceCalibrationStorage::verify_instance_exists(uint32_t instance_id) {
    std::string key = get_preference_key(instance_id);

    // 检查大小记录
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t stored_size = 0;

    if (!size_pref.load(&stored_size) || stored_size == 0) {
        return false;
    }

    // 检查数据是否存在
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t temp_data[512];

    return pref.load(&temp_data);
}





// 新增：NVS诊断方法
void PreferenceCalibrationStorage::diagnose_nvs_storage(uint32_t instance_id) {
    ESP_LOGI(TAG, "=== NVS存储诊断 (实例: 0x%08X) ===", instance_id);

    std::string key = get_preference_key(instance_id);
    ESP_LOGI(TAG, "Preference Key: %s", key.c_str());

    // 检查大小记录
    auto size_pref = global_preferences->make_preference<uint16_t>(fnv1_hash(key + "_size"));
    uint16_t actual_size = 0;
    bool size_exists = size_pref.load(&actual_size);

    ESP_LOGI(TAG, "大小记录:");
    ESP_LOGI(TAG, "  Key: %s_size", key.c_str());
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash(key + "_size"));
    ESP_LOGI(TAG, "  存在: %s", size_exists ? "是" : "否");
    ESP_LOGI(TAG, "  大小: %d 字节", actual_size);

    // 检查数据记录
    auto pref = global_preferences->make_preference<uint8_t[512]>(fnv1_hash(key));
    uint8_t temp_data[512];
    bool data_exists = pref.load(&temp_data);

    ESP_LOGI(TAG, "数据记录:");
    ESP_LOGI(TAG, "  Key: %s", key.c_str());
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash(key));
    ESP_LOGI(TAG, "  存在: %s", data_exists ? "是" : "否");

    if (data_exists && actual_size > 0) {
        ESP_LOGI(TAG, "  前16字节数据: %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
                 temp_data[0], temp_data[1], temp_data[2], temp_data[3],
                 temp_data[4], temp_data[5], temp_data[6], temp_data[7],
                 temp_data[8], temp_data[9], temp_data[10], temp_data[11],
                 temp_data[12], temp_data[13], temp_data[14], temp_data[15]);
    }

    // 检查实例列表
    auto list_pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_factory_instances"));
    uint32_t list_data[5];
    bool list_exists = list_pref.load(&list_data);

    ESP_LOGI(TAG, "实例列表:");
    ESP_LOGI(TAG, "  Key: bl0906_factory_instances");
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash("bl0906_factory_instances"));
    ESP_LOGI(TAG, "  存在: %s", list_exists ? "是" : "否");

    if (list_exists) {
        uint32_t count = list_data[0];
        ESP_LOGI(TAG, "  实例数量: %d", count);
        for (uint32_t i = 0; i < count && i < 4; i++) {
            ESP_LOGI(TAG, "    [%d] 0x%08X", i, list_data[i + 1]);
        }
    }

    // 检查备份列表
    auto backup_pref = global_preferences->make_preference<uint32_t[5]>(fnv1_hash("bl0906_instances_backup"));
    uint32_t backup_data[5];
    bool backup_exists = backup_pref.load(&backup_data);

    ESP_LOGI(TAG, "备份列表:");
    ESP_LOGI(TAG, "  Key: bl0906_instances_backup");
    ESP_LOGI(TAG, "  Hash: 0x%08X", fnv1_hash("bl0906_instances_backup"));
    ESP_LOGI(TAG, "  存在: %s", backup_exists ? "是" : "否");

    if (backup_exists) {
        uint32_t backup_count = backup_data[0];
        ESP_LOGI(TAG, "  备份实例数量: %d", backup_count);
        for (uint32_t i = 0; i < backup_count && i < 4; i++) {
            ESP_LOGI(TAG, "    [%d] 0x%08X", i, backup_data[i + 1]);
        }
    }

    ESP_LOGI(TAG, "=== NVS诊断完成 ===");
}

bool PreferenceCalibrationStorage::completely_erase_all_data() {
    uint32_t known_instances[] = {
        0x906B0001, 0x910B0001, 0x906B0002, 0x910B0002
    };

    for (uint32_t instance_id : known_instances) {
        delete_raw_data(instance_id);
    }

    instance_list_.clear();
    save_instance_list();

    global_preferences->sync();
    delay(200);

    return true;
}



}  // namespace bl0906_factory
}  // namespace esphome