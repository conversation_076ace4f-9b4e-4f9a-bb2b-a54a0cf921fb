# BL0910 校准寄存器地址修复说明

## 问题描述

在修复BL0906无法保存校准寄存器值到NVS存储的问题后，发现BL0910芯片仍然存在相同的校准数据保存问题。经过分析发现，问题的根源在于BL0910的校准寄存器地址定义错误。

## 问题根源

在 `bl0906_chip_params.h` 文件中，BL0910的CHGN（电流增益校准）寄存器地址定义错误：

### 修复前（错误的定义）：
```cpp
#ifdef BL0906_FACTORY_CHIP_MODEL_BL0910
  #define CHGN_ADDRESSES  {0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74}
#endif
```

**问题分析**：
- 地址范围 `0x6B-0x74` 实际上是 RMSGN（有效值增益校准）寄存器的地址范围
- 这导致BL0910的CHGN校准值被错误地写入到RMSGN寄存器中
- 校准数据保存和加载时使用了错误的寄存器地址，导致校准失效

### 修复后（正确的定义）：
```cpp
#ifdef BL0906_FACTORY_CHIP_MODEL_BL0910
  #define RMSGN_ADDRESSES {0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75}  // BL0910 RMSGN地址：前移1位
  #define CHGN_ADDRESSES  {0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9}  // BL0910 CHGN地址：0xA0-0xA9
#endif
```

## 寄存器地址对比

### BL0906 vs BL0910 CHGN寄存器地址对比

| 通道 | BL0906地址 | BL0910地址 | 说明 |
|------|-----------|-----------|------|
| 1 | 0xA1 | 0xA0 | 前移1位 |
| 2 | 0xA2 | 0xA1 | 前移1位 |
| 3 | 0xA3 | 0xA2 | 前移1位 |
| 4 | 0xA4 | 0xA3 | 前移1位 |
| 5 | 0xA7 | 0xA4 | 地址变化 |
| 6 | 0xA8 | 0xA5 | 地址变化 |
| 7 | - | 0xA6 | 新增 |
| 8 | - | 0xA7 | 新增 |
| 9 | - | 0xA8 | 新增 |
| 10 | - | 0xA9 | 新增 |
| 电压 | 0xAA | 0xAA | 保持不变 |

### BL0906 vs BL0910 RMSGN寄存器地址对比

| 通道 | BL0906地址 | BL0910地址 | 说明 |
|------|-----------|-----------|------|
| 1 | 0x6D | 0x6C | 前移1位 |
| 2 | 0x6E | 0x6D | 前移1位 |
| 3 | 0x6F | 0x6E | 前移1位 |
| 4 | 0x70 | 0x6F | 前移1位 |
| 5 | 0x73 | 0x70 | 地址变化 |
| 6 | 0x74 | 0x71 | 地址变化 |
| 7 | - | 0x72 | 新增 |
| 8 | - | 0x73 | 新增 |
| 9 | - | 0x74 | 新增 |
| 10 | - | 0x75 | 新增 |

## 修复内容

1. **修正BL0910的CHGN寄存器地址**：
   - 从错误的 `{0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74}`
   - 修正为正确的 `{0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8, 0xA9}`

2. **修正BL0910的RMSGN寄存器地址**：
   - 从 `{0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75, 0x76}`
   - 修正为 `{0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71, 0x72, 0x73, 0x74, 0x75}`

## 影响范围

此修复影响以下功能：

1. **校准数据保存**：BL0910的CHGN校准值现在能正确保存到NVS存储
2. **校准数据加载**：从NVS存储加载的校准值能正确应用到BL0910芯片
3. **Number组件**：BL0910的CHGN Number组件现在操作正确的寄存器地址
4. **寄存器读写**：所有涉及BL0910 CHGN和RMSGN寄存器的操作都使用正确地址

## 验证方法

可以使用以下方法验证修复是否成功：

1. **编译测试**：使用 `bl0910_calibration_test.yaml` 配置文件编译
2. **寄存器地址验证**：通过测试按钮读取CHGN寄存器地址 0xA0-0xA9
3. **校准数据保存测试**：修改CHGN值后保存到NVS，重启后验证是否正确加载
4. **对比测试**：与BL0906的校准功能进行对比，确保功能一致

## 注意事项

1. **向后兼容性**：此修复不影响BL0906的功能，BL0906继续使用原有的寄存器地址
2. **编译时选择**：通过 `BL0906_FACTORY_CHIP_MODEL_BL0910` 宏进行条件编译
3. **数据迁移**：如果之前有使用错误地址保存的校准数据，需要重新校准
4. **测试建议**：建议在实际硬件上测试校准数据的保存和加载功能

## 总结

此修复解决了BL0910芯片无法正确保存校准寄存器值到NVS存储的问题。问题的根源是寄存器地址定义错误，导致校准值被写入错误的寄存器。修复后，BL0910的校准功能应该与BL0906保持一致的可靠性。
