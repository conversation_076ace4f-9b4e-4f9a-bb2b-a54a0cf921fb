# BL0906 Factory 使用 I2C EEPROM 存储的配置示例
# 此配置会编译 I2C EEPROM 相关代码，需要外部 EEPROM 芯片

esphome:
  name: bl0906-eeprom-demo
  friendly_name: "BL0906 I2C EEPROM Storage Demo"

esp32:
  board: esp32dev
  framework:
    type: arduino

# 启用日志
logger:
  level: DEBUG

# 启用 Home Assistant API
api:
  encryption:
    key: "your-encryption-key-here"

ota:
  password: "your-ota-password-here"

wifi:
  ssid: "your-wifi-ssid"
  password: "your-wifi-password"

# I2C 总线配置（EEPROM 模式必需）
i2c:
  sda: 8
  scl: 9
  scan: true
  frequency: 100kHz

# UART 配置
uart:
  id: uart_bus
  tx_pin: 17
  rx_pin: 16
  baud_rate: 9600
  stop_bits: 1

# BL0906 Factory 组件配置 - 使用 I2C EEPROM 存储
bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  i2c_id: bus_a              # 指定 I2C 总线
  address: 0x50              # EEPROM I2C 地址（可选，默认 0x50）
  update_interval: 1s
  instance_id: 0x906B0001
  calibration_mode: true
  
  # 校准配置 - 使用 I2C EEPROM 存储
  calibration:
    enabled: true
    storage_type: eeprom       # 使用外部 I2C EEPROM
    eeprom_type: 24c04         # 选择 EEPROM 型号：24c02/24c04/24c08/24c16

# 传感器配置
sensor:
  # 基础传感器
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    voltage:
      name: "电压"
      unit_of_measurement: "V"
      accuracy_decimals: 1
    frequency:
      name: "频率"
      unit_of_measurement: "Hz"
      accuracy_decimals: 2
    temperature:
      name: "温度"
      unit_of_measurement: "°C"
      accuracy_decimals: 1
    
    # 通道传感器
    current_1:
      name: "通道1电流"
      unit_of_measurement: "A"
      accuracy_decimals: 3
    power_1:
      name: "通道1功率"
      unit_of_measurement: "W"
      accuracy_decimals: 1
    energy_1:
      name: "通道1电量"
      unit_of_measurement: "kWh"
      accuracy_decimals: 3
    
    current_2:
      name: "通道2电流"
      unit_of_measurement: "A"
      accuracy_decimals: 3
    power_2:
      name: "通道2功率"
      unit_of_measurement: "W"
      accuracy_decimals: 1
    energy_2:
      name: "通道2电量"
      unit_of_measurement: "kWh"
      accuracy_decimals: 3

# 校准数字组件（仅在校准模式下可用）
number:
  # 通道1校准
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    current_1_gain:
      name: "通道1电流增益"
      min_value: -32768
      max_value: 32767
      step: 1
    current_1_offset:
      name: "通道1电流偏置"
      min_value: -32768
      max_value: 32767
      step: 1

# 校准保存按钮（仅在校准模式下可用）
button:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    save_calibration:
      name: "保存校准数据到EEPROM"

# EEPROM 型号选择说明：
# 24c02 (256字节)  - 适合单实例应用，最多1个实例，77个校准条目
# 24c04 (512字节)  - 适合2-3个实例应用，最多3个实例，每实例80个校准条目  
# 24c08 (1024字节) - 适合4-6个实例应用，最多6个实例，每实例82个校准条目
# 24c16 (2048字节) - 适合8-12个实例应用，最多12个实例，每实例82个校准条目

# 硬件连接说明：
# EEPROM VCC -> 3.3V
# EEPROM GND -> GND  
# EEPROM SDA -> GPIO 8
# EEPROM SCL -> GPIO 9
# EEPROM A0/A1/A2 -> GND (地址 0x50) 