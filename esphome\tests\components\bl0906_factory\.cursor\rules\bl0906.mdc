---
description: 
globs: 
alwaysApply: true
---
请使用中文回复
项目基于ESPHome平台,所有代码必须符合ESPHome规范
可参考官方bl0906组件的代码,地址在: ..\components\bl0906
各通道的电参数读取通过状态机在loop()中实现,update()负责把状态机重置为IDLE状态
各个校准寄存器的读取通过read_all_calibration_registers()实现,用阻塞方式一次性读取
校准寄存器的写入通过write_calibration_register()实现,写入前需要关闭写保护,写入后读取相应寄存器验证是否写入成功
关闭写保护的操作通过turn_off_write_protect()实现,同时验证是否操作成功.
在芯片初始化过程的最后一次性读取所有的校准寄存器
对校准寄存器的各种操作通过esphome的Number组件实现

UART的通讯使用UARTDevice类提供的方法，在uart目录中