# 测试校准数据恢复功能的简化配置
# 用于验证编译错误修复

esphome:
  name: test-data-recovery
  platform: ESP32
  board: esp32dev

# 基本配置
wifi:
  ssid: "test"
  password: "test"

logger:
  level: DEBUG

# UART配置
uart:
  - id: uart_bus1
    tx_pin: GPIO1
    rx_pin: GPIO3
    baud_rate: 9600

# BL0906工厂组件
bl0906_factory:
  - id: sensor_bl0906
    communication: uart
    uart_id: uart_bus1
    update_interval: 4s
    instance_id: 0x906B0001
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: preference

# 包含恢复按钮
packages:
  bl0906_recovery: !include packages/bl0906_data_recovery_button.yaml

# 基本传感器（用于测试）
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    frequency:
      name: 'Test Frequency'
    voltage:
      name: 'Test Voltage'
    ch1:
      current:
        name: "Test Current"
      power:
        name: "Test Power"

# 基本校准数字组件（用于测试）
number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    chgn_decimal_1:
      name: "Test Current Gain"
      mode: box

# 测试按钮
button:
  - platform: template
    name: "Test Compile"
    on_press:
      lambda: |-
        ESP_LOGI("test", "编译测试成功！");
        ESP_LOGI("test", "组件ID: %s", "sensor_bl0906");
        ESP_LOGI("test", "实例ID: 0x%08X", id(sensor_bl0906)->get_instance_id());
        
        // 测试新的公有方法
        if (id(sensor_bl0906)->load_calibration_data()) {
          ESP_LOGI("test", "✅ load_calibration_data() 方法可用");
        } else {
          ESP_LOGI("test", "⚠️ load_calibration_data() 返回false");
        }
        
        // 测试诊断方法
        id(sensor_bl0906)->diagnose_nvs_storage();
        
        // 测试强制恢复方法
        id(sensor_bl0906)->force_recover_calibration_data();
