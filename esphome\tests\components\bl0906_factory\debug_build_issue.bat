@echo off
chcp 65001 >nul
setlocal EnableDelayedExpansion

echo ================================================
echo BL0906 Factory 构建问题诊断工具
echo ================================================
echo.

REM 检查当前目录
echo [诊断] 当前工作目录:
cd
echo.

REM 检查源文件
echo [诊断] 检查源文件存在性:
if exist "bl0906_core_impl.cpp" (
    echo ✓ bl0906_core_impl.cpp 存在
    for %%A in ("bl0906_core_impl.cpp") do echo   文件大小: %%~zA 字节
) else (
    echo ✗ bl0906_core_impl.cpp 不存在
)

if exist "bl0906_core_api.h" (
    echo ✓ bl0906_core_api.h 存在
    for %%A in ("bl0906_core_api.h") do echo   文件大小: %%~zA 字节
) else (
    echo ✗ bl0906_core_api.h 不存在
)
echo.

REM 检查ESP-IDF环境
echo [诊断] ESP-IDF环境检查:
if "%IDF_PATH%"=="" (
    echo ✗ IDF_PATH 未设置
    echo   请运行: %%USERPROFILE%%\esp\esp-idf\export.bat
) else (
    echo ✓ IDF_PATH = %IDF_PATH%
)
echo.

REM 检查编译器
echo [诊断] 编译器检查:
where xtensa-esp32-elf-g++.exe >nul 2>&1
if errorlevel 1 (
    echo ✗ xtensa-esp32-elf-g++.exe 未找到
    echo   编译器不在PATH中
) else (
    echo ✓ xtensa-esp32-elf-g++.exe 可用
    for /f "tokens=*" %%i in ('xtensa-esp32-elf-g++.exe --version 2^>nul') do (
        echo   版本: %%i
        goto :version_done
    )
    :version_done
)

where xtensa-esp32-elf-ar.exe >nul 2>&1
if errorlevel 1 (
    echo ✗ xtensa-esp32-elf-ar.exe 未找到
) else (
    echo ✓ xtensa-esp32-elf-ar.exe 可用
)
echo.

REM 检查路径中的特殊字符
echo [诊断] 路径分析:
set current_path=%cd%
echo 当前路径: %current_path%
call :strlen "%current_path%" path_length
echo 路径长度: %path_length% 字符
if %path_length% GTR 200 (
    echo ⚠️  路径较长，可能导致编译问题
)
echo.

REM 测试简单编译命令
echo [诊断] 测试编译命令:
if exist "bl0906_core_impl.cpp" (
    echo 尝试编译测试...
    xtensa-esp32-elf-g++.exe --version >nul 2>&1
    if errorlevel 1 (
        echo ✗ 编译器无法运行
    ) else (
        echo ✓ 编译器可以运行
        echo.
        echo 测试编译命令:
        echo xtensa-esp32-elf-g++.exe -c bl0906_core_impl.cpp -o test.o
        echo.
        xtensa-esp32-elf-g++.exe -c bl0906_core_impl.cpp -o test.o 2>&1
        if exist "test.o" (
            echo ✓ 测试编译成功
            del test.o >nul 2>&1
        ) else (
            echo ✗ 测试编译失败
        )
    )
) else (
    echo ✗ 源文件不存在，无法测试编译
)
echo.

REM 检查目录权限
echo [诊断] 目录权限检查:
echo test > test_write.tmp 2>nul
if exist "test_write.tmp" (
    echo ✓ 当前目录可写
    del test_write.tmp >nul 2>&1
) else (
    echo ✗ 当前目录不可写
)
echo.

REM 检查构建目录
echo [诊断] 构建目录检查:
if exist "build" (
    echo ✓ build 目录存在
) else (
    echo - build 目录不存在 (正常)
)

if exist "release" (
    echo ✓ release 目录存在
) else (
    echo - release 目录不存在 (正常)
)
echo.

echo ================================================
echo 诊断完成
echo ================================================
echo.
echo 建议的解决步骤:
echo 1. 确保ESP-IDF环境已正确设置
echo 2. 使用 build_precompiled_lib_fixed.bat 脚本
echo 3. 如果问题仍然存在，请检查源文件内容
echo.
pause
goto :eof

:strlen
setlocal enabledelayedexpansion
set "str=%~1"
set "len=0"
for /l %%i in (0,1,8192) do (
    if "!str:~%%i,1!" neq "" set /a len=%%i+1
)
endlocal & set "%~2=%len%"
goto :eof 