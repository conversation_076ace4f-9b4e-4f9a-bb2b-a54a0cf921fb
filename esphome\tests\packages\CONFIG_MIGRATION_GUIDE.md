# BL0906配置文件迁移指南

## 概述

本文档说明了如何将原有的基于globals的电量累计系统迁移到新的电量持久化存储系统。

## 主要变更

### 1. 移除globals系统

**原有配置**:
```yaml
globals:
  - id: ch1_energy_
    type: float
    restore_value: yes
  - id: ch1_energy_last
    type: float
    restore_value: yes
  # ... 更多globals定义
```

**新配置**:
```yaml
# 启用preferences组件用于数据持久化
preferences:
  flash_write_interval: 1min  # 每分钟写入一次flash，平衡数据安全和flash寿命
```

### 2. 简化energy传感器配置

**原有配置**:
```yaml
energy_1:
  id: ch1_energy
  name: "${ch1} energy"
  icon: "mdi:lightning-bolt"
  web_server:
      sorting_group_id: energy
  on_value:
    then:
      - if:
          condition:
            sensor.in_range:
              id: ch1_energy
              above: 0.01
          then:
            globals.set:
              id: ch1_energy_
              value: !lambda return id(ch1_energy_last) + x;
          else:
            - globals.set:
                id: ch1_energy_last
                value: !lambda return id(ch1_energy_);
```

**新配置**:
```yaml
energy_1:
  id: ch1_energy
  name: "${ch1} energy"
  icon: "mdi:lightning-bolt"
  web_server:
      sorting_group_id: energy
  accuracy_decimals: 6
  unit_of_measurement: kWh
```

### 3. 新增持久化累计电量传感器

**新增配置**:
```yaml
# 各通道累计电量传感器（持久化存储）
total_energy_1:
  name: "${ch1} Total Energy"
  icon: "mdi:counter"
  unit_of_measurement: kWh
  device_class: energy
  state_class: total_increasing
  accuracy_decimals: 3
  web_server:
      sorting_group_id: energy

# 总累计电量传感器（持久化存储）
total_energy_sum:
  name: "6-ch Sum Total Energy"
  icon: "mdi:sigma"
  unit_of_measurement: kWh
  device_class: energy
  state_class: total_increasing
  accuracy_decimals: 3
  web_server:
      sorting_group_id: energy
```

### 4. 移除template传感器

**原有配置**:
```yaml
- platform: template
  name: "${ch1} Total Energy"
  id: ch1_total_energy
  icon: "mdi:counter"
  unit_of_measurement: kWh
  device_class: energy
  state_class: total_increasing
  accuracy_decimals: 2
  lambda: |-
    return id(ch1_energy_);
  update_interval: 10s
```

**新配置**: 不再需要，由BL0906Factory组件直接提供

### 5. 新增控制按钮

**新增配置**:
```yaml
button:
  # 重置累计电量
  - platform: template
    name: "Reset Total Energy"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->reset_energy_data();

  # 强制保存电量数据
  - platform: template
    name: "Force Save Energy Data"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->force_save_energy_data();

  # 重新加载电量数据
  - platform: template
    name: "Reload Energy Data"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->reload_energy_data();
```

### 6. 新增控制开关

**新增配置**:
```yaml
switch:
  # 电量持久化开关
  - platform: template
    name: "Energy Persistence"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(true);
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(false);
```

### 7. 新增状态显示

**新增配置**:
```yaml
text_sensor:
  - platform: template
    name: "Energy Persistence Status"
    icon: "mdi:information"
    lambda: |-
      auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
      float total = bl0906->get_total_energy_sum();
      if (total > 0) {
        return {"Active - Total: " + to_string(total) + " kWh"};
      } else {
        return {"Initializing..."};
      }
    update_interval: 30s
```

## 迁移步骤

### 1. 备份原配置
```bash
cp bl0906_factory_6ch_calib.yaml bl0906_factory_6ch_calib.yaml.backup
```

### 2. 更新组件代码
确保使用最新的BL0906Factory组件代码，包含电量持久化功能。

### 3. 修改配置文件
按照上述变更说明修改配置文件。

### 4. 编译和部署
```bash
esphome compile your_device.yaml
esphome upload your_device.yaml
```

### 5. 验证功能
- 检查累计电量传感器是否正常显示
- 测试重置按钮功能
- 验证断电重启后数据是否保持

## 优势对比

### 原有系统 vs 新系统

| 特性 | 原有系统 | 新系统 |
|------|----------|--------|
| 数据存储 | globals + restore_value | preferences + flash |
| 断电处理 | 手动逻辑 | 自动检测重启 |
| 配置复杂度 | 高（大量globals和template） | 低（直接传感器） |
| 性能 | 中等 | 高（原生实现） |
| 可靠性 | 中等 | 高（校验和保护） |
| 维护性 | 低 | 高 |

## 注意事项

1. **数据迁移**: 新系统不会自动迁移原有globals中的数据，需要手动记录当前累计值
2. **配置兼容性**: 确保ESPHome版本支持preferences组件
3. **Flash寿命**: 合理设置flash_write_interval以保护flash寿命
4. **调试信息**: 启用DEBUG日志级别可查看详细的电量累计过程

## 故障排除

### 1. 累计电量不更新
- 检查preferences组件是否正确配置
- 确认BL0906Factory组件版本是否最新
- 查看日志中的初始化信息

### 2. 数据重置后丢失
- 检查flash_write_interval设置
- 确认设备有足够的flash空间
- 查看数据校验相关日志

### 3. 断电重启后数据异常
- 查看芯片重启检测日志
- 确认脉冲计数初始化是否正常
- 检查数据完整性校验结果

## 总结

新的电量持久化存储系统提供了更可靠、更简洁的电量统计解决方案。通过自动处理断电重启、数据校验和智能累计算法，大大提高了系统的稳定性和易用性。
