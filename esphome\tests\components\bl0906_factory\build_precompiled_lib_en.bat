@echo off
setlocal EnableDelayedExpansion

REM BL0906 Factory Precompiled Library Build Script (English Version)
REM This script compiles core algorithms into a precompiled library for IP protection

REM Project Configuration
set PROJECT_NAME=bl0906_factory
set CORE_LIB_NAME=libbl0906_core.a
set VERSION=2.0.0

REM Directory Configuration
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%
set BUILD_DIR=%PROJECT_DIR%build
set RELEASE_DIR=%PROJECT_DIR%release
set TEMP_DIR=%PROJECT_DIR%temp

REM Source Files Configuration (using full paths)
set CORE_SOURCES=%PROJECT_DIR%bl0906_core_impl.cpp
set HEADER_FILES=%PROJECT_DIR%bl0906_core_api.h

REM Compiler Configuration (requires ESP-IDF environment)
set CC=xtensa-esp32-elf-gcc.exe
set CXX=xtensa-esp32-elf-g++.exe
set AR=xtensa-esp32-elf-ar.exe
set STRIP=xtensa-esp32-elf-strip.exe

REM Compilation Options
set CFLAGS=-Os -g0 -DNDEBUG -ffunction-sections -fdata-sections -fstrict-aliasing -mlongcalls
set CXXFLAGS=%CFLAGS% -std=c++17 -fno-rtti -fno-exceptions
set INCLUDES=-I. -I"%PROJECT_DIR%"

echo ================================================
echo   BL0906 Factory Precompiled Library Builder v%VERSION%
echo   English Version (No Encoding Issues)
echo ================================================
echo.

:check_dependencies
echo [STEP] Checking build dependencies...

REM Check ESP-IDF environment
if "%IDF_PATH%"=="" (
    echo [ERROR] IDF_PATH environment variable not set
    echo [INFO] Please run ESP-IDF export.bat script first
    echo [INFO] Example: %%USERPROFILE%%\esp\esp-idf\export.bat
    echo.
    echo [SOLUTION] Follow these steps:
    echo 1. Open Command Prompt
    echo 2. Run: %%USERPROFILE%%\esp\esp-idf\export.bat
    echo 3. In the same command prompt, run this script again
    echo.
    pause
    exit /b 1
)

REM Check compilers
where %CC% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %CC% compiler not found
    echo [INFO] Please ensure ESP-IDF environment is properly configured
    echo [INFO] ESP32 toolchain not found in PATH
    echo.
    echo [DEBUG] Current PATH includes:
    echo %PATH%
    echo.
    pause
    exit /b 1
)

where %CXX% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %CXX% compiler not found
    echo [INFO] Please ensure ESP-IDF environment is properly configured
    pause
    exit /b 1
)

where %AR% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] %AR% archiver not found
    echo [INFO] Please ensure ESP-IDF environment is properly configured
    pause
    exit /b 1
)

echo [SUCCESS] All dependencies check passed

:prepare_directories
echo [STEP] Preparing build directories...

REM Clean old build directories
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
)

if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%"
)

REM Create directories
mkdir "%BUILD_DIR%" 2>nul
mkdir "%RELEASE_DIR%" 2>nul
mkdir "%TEMP_DIR%" 2>nul

echo [SUCCESS] Directory preparation completed

:validate_sources
echo [STEP] Validating source files...

if not exist "%CORE_SOURCES%" (
    echo [ERROR] Source file not found: %CORE_SOURCES%
    echo [INFO] Please ensure you run this script in the correct directory
    pause
    exit /b 1
)
echo [INFO] Found bl0906_core_impl.cpp

if not exist "%HEADER_FILES%" (
    echo [ERROR] Header file not found: %HEADER_FILES%
    echo [INFO] Please ensure you run this script in the correct directory
    pause
    exit /b 1
)
echo [INFO] Found bl0906_core_api.h

echo [SUCCESS] Source file validation completed

:compile_core_library
echo [STEP] Compiling core library...

cd /d "%BUILD_DIR%"

REM Compile source files
set object_file=bl0906_core_impl.o

echo [INFO] Compiling bl0906_core_impl.cpp...
echo [INFO] Source file path: %CORE_SOURCES%
echo [INFO] Output file: %object_file%
echo.

REM Display compilation command details for debugging
echo [DEBUG] Compilation command details:
echo   Compiler: %CXX%
echo   Source: "%CORE_SOURCES%"
echo   Output: "%object_file%"
echo   Flags: %CXXFLAGS%
echo   Includes: %INCLUDES%
echo.

REM Compile C++ source file
"%CXX%" %CXXFLAGS% %INCLUDES% -c "%CORE_SOURCES%" -o "%object_file%"

if errorlevel 1 (
    echo [ERROR] Compilation failed: bl0906_core_impl.cpp
    echo [INFO] Please check source code for syntax errors
    echo [DEBUG] Try running the compilation command manually for debugging
    pause
    exit /b 1
)

echo [INFO] Successfully compiled bl0906_core_impl.cpp to %object_file%

REM Verify object file was generated
if not exist "%object_file%" (
    echo [ERROR] Object file not generated: %object_file%
    pause
    exit /b 1
)

REM Create static library
echo [INFO] Creating static library %CORE_LIB_NAME%...
"%AR%" rcs "%CORE_LIB_NAME%" "%object_file%"

if errorlevel 1 (
    echo [ERROR] Static library creation failed
    pause
    exit /b 1
)

REM Verify static library was generated
if not exist "%CORE_LIB_NAME%" (
    echo [ERROR] Static library file not generated: %CORE_LIB_NAME%
    pause
    exit /b 1
)

REM Optimize library file
echo [INFO] Optimizing library file...
"%STRIP%" --strip-unneeded "%CORE_LIB_NAME%" 2>nul

if errorlevel 1 (
    echo [WARNING] Library optimization failed, but this does not affect functionality
)

echo [SUCCESS] Core library compilation completed

:generate_version_info
echo [STEP] Generating version information...

set version_file=%BUILD_DIR%\VERSION

REM Get current time
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do (
    set build_date=%%a/%%b/%%c
)
for /f "tokens=1-2 delims=: " %%a in ('time /t') do (
    set build_time=%%a:%%b
)

REM Try to get Git commit hash
git rev-parse --short HEAD >temp_hash.txt 2>nul
if errorlevel 1 (
    set build_hash=unknown
) else (
    set /p build_hash=<temp_hash.txt
)
if exist temp_hash.txt del temp_hash.txt

echo BL0906 Factory Precompiled Library Version Information > "%version_file%"
echo ======================================================== >> "%version_file%"
echo Version: %VERSION% >> "%version_file%"
echo Build Date: %build_date% %build_time% >> "%version_file%"
echo Git Commit: %build_hash% >> "%version_file%"
echo Compiler: %CXX% >> "%version_file%"
echo Target Architecture: ESP32 (Xtensa) >> "%version_file%"
echo Build Platform: Windows (English Version) >> "%version_file%"
echo. >> "%version_file%"
echo Core Features: >> "%version_file%"
echo - BL0906/BL0910 dual chip support >> "%version_file%"
echo - Runtime chip model switching >> "%version_file%"
echo - Chip parameter management (integrated from bl0906_chip_params.h) >> "%version_file%"
echo - Calibration coefficient calculation (integrated from bl0906_calibration.h) >> "%version_file%"
echo - Register address mapping algorithm >> "%version_file%"
echo - Data conversion algorithm >> "%version_file%"
echo - Write protection unlock algorithm >> "%version_file%"
echo - Frequency mode setting >> "%version_file%"
echo - Voltage sampling mode support >> "%version_file%"

echo [SUCCESS] Version information generation completed

:create_release_package
echo [STEP] Creating release package...

REM Copy core library file
copy "%BUILD_DIR%\%CORE_LIB_NAME%" "%RELEASE_DIR%\" >nul

REM Copy header file
copy "%HEADER_FILES%" "%RELEASE_DIR%\" >nul

REM Copy version information
copy "%BUILD_DIR%\VERSION" "%RELEASE_DIR%\" >nul

REM Generate release documentation
echo # BL0906 Factory Precompiled Library (Windows English Build) > "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo ## Overview >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo This is the precompiled core library for BL0906 Factory component. >> "%RELEASE_DIR%\README.md"
echo Built on Windows using ESP-IDF toolchain with English interface. >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo ## Build Information >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo - Version: %VERSION% >> "%RELEASE_DIR%\README.md"
echo - Platform: Windows >> "%RELEASE_DIR%\README.md"
echo - Compiler: ESP32 Xtensa GCC >> "%RELEASE_DIR%\README.md"
echo - Language: English (No encoding issues) >> "%RELEASE_DIR%\README.md"
echo - Path Handling: Fixed >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo ## Files >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo - libbl0906_core.a - Precompiled static library >> "%RELEASE_DIR%\README.md"
echo - bl0906_core_api.h - C interface header file >> "%RELEASE_DIR%\README.md"
echo - VERSION - Version information >> "%RELEASE_DIR%\README.md"
echo - README.md - Documentation >> "%RELEASE_DIR%\README.md"

echo [SUCCESS] Release package creation completed

:show_library_info
echo [STEP] Library file information...

set lib_path=%RELEASE_DIR%\%CORE_LIB_NAME%

REM Get file size
for %%A in ("%lib_path%") do set lib_size=%%~zA

echo.
echo Library file path: %lib_path%
echo Library file size: %lib_size% bytes

REM Display file list
echo.
echo [INFO] Release package contents:
dir "%RELEASE_DIR%" /b
echo.

:cleanup
echo [STEP] Cleaning up temporary files...

if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
)

if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%"
)

echo [SUCCESS] Cleanup completed

:finish
echo.
echo ================================================
echo [SUCCESS] Precompiled library build completed!
echo ================================================
echo.
echo Release files located at: %RELEASE_DIR%
echo Core library file: %RELEASE_DIR%\%CORE_LIB_NAME%
echo.
echo File verification:
if exist "%RELEASE_DIR%\%CORE_LIB_NAME%" (
    echo   Static library file generated successfully
) else (
    echo   Static library file generation failed
)

if exist "%RELEASE_DIR%\bl0906_core_api.h" (
    echo   Header file copied successfully
) else (
    echo   Header file copy failed
)

if exist "%RELEASE_DIR%\VERSION" (
    echo   Version information generated successfully
) else (
    echo   Version information generation failed
)

echo.
echo Next steps:
echo 1. Integrate release files into ESPHome component
echo 2. Update thin wrapper layer to use precompiled library
echo 3. Test component functionality
echo 4. Deploy to production environment
echo.
echo If you encounter ESP-IDF setup issues:
echo 1. Download ESP-IDF from: https://github.com/espressif/esp-idf
echo 2. Run the installer or manual setup
echo 3. Execute: [ESP-IDF-PATH]\export.bat
echo 4. Run this script in the same command prompt
echo.
pause 