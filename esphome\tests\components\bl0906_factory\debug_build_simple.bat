@echo off
chcp 65001 >nul

echo ================================================
echo BL0906 Factory 构建问题诊断工具 (简化版)
echo ================================================
echo.

REM 检查当前目录
echo [诊断] 当前工作目录:
echo %cd%
echo.

REM 检查源文件
echo [诊断] 检查关键文件:
if exist "bl0906_core_impl.cpp" (
    echo ✓ bl0906_core_impl.cpp 存在
) else (
    echo ✗ bl0906_core_impl.cpp 不存在
)

if exist "bl0906_core_api.h" (
    echo ✓ bl0906_core_api.h 存在
) else (
    echo ✗ bl0906_core_api.h 不存在
)
echo.

REM 检查ESP-IDF环境
echo [诊断] ESP-IDF环境:
if "%IDF_PATH%"=="" (
    echo ✗ IDF_PATH 未设置
    echo   解决方法: 运行 ESP-IDF 的 export.bat 脚本
) else (
    echo ✓ IDF_PATH 已设置
    echo   路径: %IDF_PATH%
)
echo.

REM 检查编译器
echo [诊断] 编译器检查:
xtensa-esp32-elf-g++.exe --version >nul 2>&1
if errorlevel 1 (
    echo ✗ xtensa-esp32-elf-g++.exe 不可用
    echo   请确保ESP-IDF环境已正确设置
) else (
    echo ✓ xtensa-esp32-elf-g++.exe 可用
)

xtensa-esp32-elf-ar.exe --version >nul 2>&1
if errorlevel 1 (
    echo ✗ xtensa-esp32-elf-ar.exe 不可用
) else (
    echo ✓ xtensa-esp32-elf-ar.exe 可用
)
echo.

REM 简单编译测试
echo [诊断] 编译测试:
if exist "bl0906_core_impl.cpp" (
    echo 尝试简单编译测试...
    xtensa-esp32-elf-g++.exe -c bl0906_core_impl.cpp -o test_compile.o >nul 2>&1
    if exist "test_compile.o" (
        echo ✓ 编译测试成功
        del test_compile.o >nul 2>&1
    ) else (
        echo ✗ 编译测试失败
        echo   可能的原因:
        echo   1. 源文件有语法错误
        echo   2. 缺少必要的头文件
        echo   3. 编译器配置问题
    )
) else (
    echo ✗ 无法进行编译测试 (源文件不存在)
)
echo.

REM 检查目录权限
echo [诊断] 目录权限:
echo test > test_permission.tmp 2>nul
if exist "test_permission.tmp" (
    echo ✓ 当前目录可写
    del test_permission.tmp >nul 2>&1
) else (
    echo ✗ 当前目录不可写
)
echo.

echo ================================================
echo 诊断结果总结
echo ================================================
echo.

REM 生成建议
if "%IDF_PATH%"=="" (
    echo 🔧 主要问题: ESP-IDF环境未设置
    echo 解决步骤:
    echo 1. 找到ESP-IDF安装目录
    echo 2. 运行: [ESP-IDF目录]\export.bat
    echo 3. 在同一个命令提示符中重新运行构建脚本
) else (
    if exist "bl0906_core_impl.cpp" (
        echo 🔧 环境配置正常，可以尝试构建
        echo 建议使用: build_precompiled_lib_fixed.bat
    ) else (
        echo 🔧 源文件缺失
        echo 请确保在正确的目录中运行脚本
    )
)

echo.
echo 如果问题仍然存在，请检查:
echo - ESP-IDF版本是否兼容
echo - 路径中是否包含特殊字符
echo - 是否有足够的磁盘空间
echo.
pause 