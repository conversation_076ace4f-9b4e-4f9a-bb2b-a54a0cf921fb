#include "energy_statistics_manager.h"
#include "bl0906_factory.h"
#include "bl0906_calibration.h"
#include "esphome/core/log.h"
#include "esphome/components/time/real_time_clock.h"
#include "esphome/core/hal.h"
#include "esphome/core/application.h"

// 添加使用命名空间，以便可以访问常量
using namespace esphome::bl0906_factory;

// 避免命名空间冲突
namespace esphome_time = esphome::time;

namespace esphome {
namespace bl0906_factory {

static const char *const ENERGY_STATS_TAG = "energy_stats";

EnergyStatisticsManager::EnergyStatisticsManager(BL0906Factory* parent) : PollingComponent(60000), parent_(parent) {
  initialized_.store(false);
  time_initialized_.store(false);
  last_check_timestamp_.store(0);

  ESP_LOGI(ENERGY_STATS_TAG, "EnergyStatisticsManager构造函数完成，采用分步初始化策略");
}

void EnergyStatisticsManager::setup() {
  ESP_LOGI(ENERGY_STATS_TAG, "开始电量统计管理器基础初始化...");

  // 喂狗，防止看门狗超时
  App.feed_wdt();

  // 加载统计数据
  load_statistics_data_safe();

  // 直接进行基础初始化（时间在此阶段肯定无效）
  basic_initialization();
}

void EnergyStatisticsManager::set_time_component(esphome_time::RealTimeClock *time_comp) {
  time_component_ = time_comp;
  ESP_LOGI(ENERGY_STATS_TAG, "设置时间组件: %s", time_comp ? "成功" : "无效");
}

void EnergyStatisticsManager::update_persistent_cf_count(int channel, uint32_t current_cf_count) {
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  if (channel < 0 || channel >= max_channels) {
    ESP_LOGW(ENERGY_STATS_TAG, "无效通道: %d", channel);
    return;
  }

  if (!initialized_.load() || !current_persistent_cf_count_) {
    ESP_LOGV(ENERGY_STATS_TAG, "统计管理器未初始化或内存未分配，跳过通道%d的CF_count更新", channel + 1);
    return;
  }

  // 直接设置当前持久化CF_count值
  current_persistent_cf_count_[channel].store(current_cf_count);

  ESP_LOGV(ENERGY_STATS_TAG, "通道%d持久化CF_count设置为: %u", channel + 1, current_cf_count);
}

void EnergyStatisticsManager::update_persistent_cf_count_sum(uint32_t current_cf_count_sum) {
  if (!initialized_.load() || !current_persistent_cf_count_) {
    ESP_LOGV(ENERGY_STATS_TAG, "统计管理器未初始化或内存未分配，跳过总CF_count更新");
    return;
  }

  // 总和存储在数组的最后一个元素
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  current_persistent_cf_count_[max_channels].store(current_cf_count_sum);

  ESP_LOGV(ENERGY_STATS_TAG, "总持久化CF_count设置为: %u", current_cf_count_sum);
}

void EnergyStatisticsManager::check_period_changes() {
  if (!time_initialized_.load() || !time_component_ || !is_time_valid()) {
    return;
  }
  
  ESPTime current_time = get_current_time();
  time_t current_timestamp = current_time.timestamp;
  time_t last_timestamp = last_check_timestamp_.load();
  
  // 避免频繁检查（至少间隔1分钟）
  if (current_timestamp - last_timestamp < 60) {
    return;
  }
  
  // 更新检查时间戳
  last_check_timestamp_.store(current_timestamp);
  
  // 如果是第一次检查，不执行周期变化处理
  if (last_timestamp == 0) {
    return;
  }
  
  ESPTime last_time;
  last_time.timestamp = last_timestamp;
  last_time.recalc_timestamp_utc();
  
  // 检查各种周期变化
  if (is_new_year(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新年: %d -> %d", last_time.year, current_time.year);
    handle_new_year();
  } else if (is_new_month(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新月: %d-%d -> %d-%d", 
             last_time.year, last_time.month, current_time.year, current_time.month);
    handle_new_month();
  } else if (is_new_week(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新周");
    handle_new_week();
  } else if (is_new_day(last_time, current_time)) {
    ESP_LOGI(ENERGY_STATS_TAG, "检测到新日: %d-%d-%d -> %d-%d-%d",
             last_time.year, last_time.month, last_time.day_of_month,
             current_time.year, current_time.month, current_time.day_of_month);
    handle_new_day();
  }
}

void EnergyStatisticsManager::update_statistics_on_save() {
  if (!initialized_.load() || !statistics_mutex_ || !unified_statistics_ || !current_persistent_cf_count_) {
    ESP_LOGV(ENERGY_STATS_TAG, "统计管理器未初始化或内存未分配，跳过保存更新");
    return;
  }

  // 线程安全地更新统计数据
  std::lock_guard<std::mutex> lock(*statistics_mutex_);

  // 防止并发更新
  if (unified_statistics_->updating.exchange(true)) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计数据正在更新中，跳过本次保存更新");
    return;
  }

  // 更新当前持久化CF_count
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }

  ESPTime current_time = get_current_time();
  unified_statistics_->last_update_timestamp = current_time.timestamp;
  unified_statistics_->updating.store(false);
  
  ESP_LOGD(ENERGY_STATS_TAG, "统计数据在保存时已更新");
}

void EnergyStatisticsManager::update_statistics_on_date_change() {
  // 这个方法在日期变更时被调用，用于更新时间点快照
  // 具体实现在handle_new_day等方法中
  ESP_LOGD(ENERGY_STATS_TAG, "日期变更时更新统计数据");
}

void EnergyStatisticsManager::basic_initialization() {
  ESP_LOGI(ENERGY_STATS_TAG, "开始基础初始化（内存分配）...");

  // 分配所有必要的内存
  ESP_LOGI(ENERGY_STATS_TAG, "分配互斥锁...");
  statistics_mutex_.reset(new std::mutex());
  if (!statistics_mutex_) {
    ESP_LOGE(ENERGY_STATS_TAG, "分配互斥锁失败");
    return;
  }

  ESP_LOGI(ENERGY_STATS_TAG, "分配统计数据结构...");
  unified_statistics_.reset(new OptimizedEnergyStatistics());
  if (!unified_statistics_) {
    ESP_LOGE(ENERGY_STATS_TAG, "分配统计数据结构失败");
    return;
  }

  // 获取实际通道数，动态分配数组大小
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  int array_size = max_channels + 1;  // +1 for sum channel
  ESP_LOGI(ENERGY_STATS_TAG, "检测到芯片通道数: %d，分配数组大小: %d", max_channels, array_size);

  ESP_LOGI(ENERGY_STATS_TAG, "分配传感器指针数组...");
  yesterday_energy_sensors_.reset(new sensor::Sensor*[array_size]);
  today_energy_sensors_.reset(new sensor::Sensor*[array_size]);
  week_energy_sensors_.reset(new sensor::Sensor*[array_size]);
  month_energy_sensors_.reset(new sensor::Sensor*[array_size]);
  year_energy_sensors_.reset(new sensor::Sensor*[array_size]);

  if (!yesterday_energy_sensors_ || !today_energy_sensors_ ||
      !week_energy_sensors_ || !month_energy_sensors_ || !year_energy_sensors_) {
    ESP_LOGE(ENERGY_STATS_TAG, "分配传感器指针数组失败");
    return;
  }

  // 初始化传感器指针为nullptr
  for (int i = 0; i < array_size; i++) {
    yesterday_energy_sensors_[i] = nullptr;
    today_energy_sensors_[i] = nullptr;
    week_energy_sensors_[i] = nullptr;
    month_energy_sensors_[i] = nullptr;
    year_energy_sensors_[i] = nullptr;
  }

  ESP_LOGI(ENERGY_STATS_TAG, "分配原子变量数组...");
  current_persistent_cf_count_.reset(new std::atomic<uint32_t>[array_size]);
  if (!current_persistent_cf_count_) {
    ESP_LOGE(ENERGY_STATS_TAG, "分配原子变量数组失败");
    return;
  }

  for (int i = 0; i < array_size; i++) {
    current_persistent_cf_count_[i].store(0);
  }

  // 标记基础初始化完成，但时间相关功能未启用
  initialized_.store(true);  // 基础功能可用
  ESP_LOGI(ENERGY_STATS_TAG, "基础初始化完成，等待时间同步后启用时间相关功能");
  
  // 处理所有延迟的传感器注册请求
  process_pending_sensor_registrations();
}

void EnergyStatisticsManager::time_related_initialization() {
  ESP_LOGI(ENERGY_STATS_TAG, "开始时间相关初始化...");

  if (!statistics_mutex_ || !unified_statistics_) {
    ESP_LOGE(ENERGY_STATS_TAG, "基础初始化未完成，无法进行时间相关初始化");
    return;
  }

  // 获取实际通道数
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  int array_size = max_channels + 1;  // +1 for sum channel

  // 检查是否需要创建快照
  bool need_create_snapshot = false;
  {
    std::lock_guard<std::mutex> lock(*statistics_mutex_);

    // 检查各通道的快照是否都为0
    for (int channel = 0; channel < array_size; channel++) {
      for (int period = 0; period < 5; period++) {
        if (unified_statistics_->period_persistent_cf_count[channel][period] == 0) {
          need_create_snapshot = true;
          ESP_LOGI(ENERGY_STATS_TAG, "检测到通道%d周期%d的快照为0，需要生成快照",
                   channel < max_channels ? channel + 1 : 0, period);  // 总和通道显示为0
          break;
        }
      }
      if (need_create_snapshot) break;
    }

    // 检查时间快照是否为0
    if (!need_create_snapshot) {
      for (int i = 0; i < 5; i++) {
        if (unified_statistics_->period_times[i].timestamp == 0) {
          need_create_snapshot = true;
          ESP_LOGI(ENERGY_STATS_TAG, "检测到周期%d的时间快照为0，需要生成快照", i);
          break;
        }
      }
    }
  }

  // 创建初始快照
  if (need_create_snapshot) {
    ESP_LOGI(ENERGY_STATS_TAG, "立即触发生成初始快照");
    create_current_snapshot_safe();
  } else {
    ESP_LOGI(ENERGY_STATS_TAG, "快照数据完整，跳过快照生成");
  }

  ESP_LOGI(ENERGY_STATS_TAG, "时间相关初始化完成，电量统计管理器功能完全启用");
}

void EnergyStatisticsManager::update() {
  // 检查是否需要进行时间相关初始化
  if (initialized_.load() && !time_initialized_ && time_component_ && is_time_valid()) {
    ESP_LOGI(ENERGY_STATS_TAG, "时间组件已准备好，开始时间相关初始化");
    time_related_initialization();
    time_initialized_ = true;
  }

  // 如果基础初始化未完成，跳过更新
  if (!initialized_.load()) {
    ESP_LOGV(ENERGY_STATS_TAG, "基础初始化未完成，跳过统计传感器更新");
    return;
  }

  // 检查时间相关初始化状态
  if (!time_initialized_.load()) {
    ESP_LOGV(ENERGY_STATS_TAG, "时间相关初始化未完成，跳过统计传感器更新");
    return;
  }

  // 更新各通道的统计传感器
  if (!yesterday_energy_sensors_ || !today_energy_sensors_ || !week_energy_sensors_ || 
      !month_energy_sensors_ || !year_energy_sensors_) {
    ESP_LOGW(ENERGY_STATS_TAG, "传感器数组未分配，跳过传感器更新");
    return;
  }

  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  int sensors_updated = 0;
  
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    // 昨日电量
    if (yesterday_energy_sensors_[channel]) {
      float energy = (channel < max_channels) ?
        calculate_energy_for_period(channel, EnergyPeriod::YESTERDAY) :
        calculate_total_energy_for_period(EnergyPeriod::YESTERDAY);
      yesterday_energy_sensors_[channel]->publish_state(energy);
      sensors_updated++;
      ESP_LOGV(ENERGY_STATS_TAG, "通道%d昨日电量: %.6f kWh", 
               channel < max_channels ? channel + 1 : 0, energy);
    }

    // 今日电量
    if (today_energy_sensors_[channel]) {
      float energy = (channel < max_channels) ?
        calculate_energy_for_period(channel, EnergyPeriod::TODAY) :
        calculate_total_energy_for_period(EnergyPeriod::TODAY);
      today_energy_sensors_[channel]->publish_state(energy);
      sensors_updated++;
      ESP_LOGV(ENERGY_STATS_TAG, "通道%d今日电量: %.6f kWh", 
               channel < max_channels ? channel + 1 : 0, energy);
    }

    // 本周电量
    if (week_energy_sensors_[channel]) {
      float energy = (channel < max_channels) ?
        calculate_energy_for_period(channel, EnergyPeriod::THIS_WEEK) :
        calculate_total_energy_for_period(EnergyPeriod::THIS_WEEK);
      week_energy_sensors_[channel]->publish_state(energy);
      sensors_updated++;
      ESP_LOGV(ENERGY_STATS_TAG, "通道%d本周电量: %.6f kWh", 
               channel < max_channels ? channel + 1 : 0, energy);
    }

    // 本月电量
    if (month_energy_sensors_[channel]) {
      float energy = (channel < max_channels) ?
        calculate_energy_for_period(channel, EnergyPeriod::THIS_MONTH) :
        calculate_total_energy_for_period(EnergyPeriod::THIS_MONTH);
      month_energy_sensors_[channel]->publish_state(energy);
      sensors_updated++;
      ESP_LOGV(ENERGY_STATS_TAG, "通道%d本月电量: %.6f kWh", 
               channel < max_channels ? channel + 1 : 0, energy);
    }

    // 本年电量
    if (year_energy_sensors_[channel]) {
      float energy = (channel < max_channels) ?
        calculate_energy_for_period(channel, EnergyPeriod::THIS_YEAR) :
        calculate_total_energy_for_period(EnergyPeriod::THIS_YEAR);
      year_energy_sensors_[channel]->publish_state(energy);
      sensors_updated++;
      ESP_LOGV(ENERGY_STATS_TAG, "通道%d本年电量: %.6f kWh", 
               channel < max_channels ? channel + 1 : 0, energy);
    }
  }
  
  // 检查周期变化
  check_period_changes();
  
  ESP_LOGD(ENERGY_STATS_TAG, "统计传感器已更新，共更新%d个传感器", sensors_updated);
  
  // 如果没有更新任何传感器，输出传感器状态
  if (sensors_updated == 0) {
    ESP_LOGW(ENERGY_STATS_TAG, "没有更新任何统计传感器，检查传感器注册状态");
    print_sensor_status();
  }
}

void EnergyStatisticsManager::set_sensor(StatisticsSensorType type, sensor::Sensor *sensor, int channel) {
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  if (channel < 0 || channel > max_channels) {  // allow sum channel (max_channels index)
    ESP_LOGW(ENERGY_STATS_TAG, "无效通道索引: %d", channel);
    return;
  }

  // 如果内存还未分配，将传感器设置请求保存到队列中
  if (!initialized_.load()) {
    ESP_LOGI(ENERGY_STATS_TAG, "统计管理器未初始化，将传感器加入延迟注册队列: 类型=%d, 通道=%d", 
             static_cast<int>(type), channel);
    
    // 存储传感器注册请求到队列中
    PendingSensorRegistration pending_reg;
    pending_reg.type = type;
    pending_reg.sensor = sensor;
    pending_reg.channel = channel;
    pending_sensor_registrations_.push_back(pending_reg);
    return;
  }

  if (!yesterday_energy_sensors_ || !today_energy_sensors_ || !week_energy_sensors_ || 
      !month_energy_sensors_ || !year_energy_sensors_) {
    ESP_LOGW(ENERGY_STATS_TAG, "传感器数组未分配，无法设置传感器");
    return;
  }

  switch (type) {
    case StatisticsSensorType::YESTERDAY_ENERGY:
    case StatisticsSensorType::YESTERDAY_TOTAL_ENERGY:
      yesterday_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::TODAY_ENERGY:
    case StatisticsSensorType::TODAY_TOTAL_ENERGY:
      today_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::WEEK_ENERGY:
    case StatisticsSensorType::WEEK_TOTAL_ENERGY:
      week_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::MONTH_ENERGY:
    case StatisticsSensorType::MONTH_TOTAL_ENERGY:
      month_energy_sensors_[channel] = sensor;
      break;
    case StatisticsSensorType::YEAR_ENERGY:
    case StatisticsSensorType::YEAR_TOTAL_ENERGY:
      year_energy_sensors_[channel] = sensor;
      break;
  }

  ESP_LOGI(ENERGY_STATS_TAG, "设置统计传感器: 类型=%d, 通道=%d", static_cast<int>(type), channel);
}

// 线程安全的数据访问方法
void EnergyStatisticsManager::get_statistics_data_safe(OptimizedEnergyStatistics& out_data) const {
  if (!initialized_.load() || !statistics_mutex_ || !unified_statistics_) {
    ESP_LOGV(ENERGY_STATS_TAG, "统计管理器未初始化或内存未分配，无法获取统计数据");
    return;
  }

  std::lock_guard<std::mutex> lock(*statistics_mutex_);
  out_data = *unified_statistics_;
}

void EnergyStatisticsManager::set_statistics_data_safe(const OptimizedEnergyStatistics& in_data) {
  if (!initialized_.load() || !statistics_mutex_ || !unified_statistics_) {
    ESP_LOGV(ENERGY_STATS_TAG, "统计管理器未初始化或内存未分配，无法设置统计数据");
    return;
  }

  std::lock_guard<std::mutex> lock(*statistics_mutex_);
  *unified_statistics_ = in_data;
}

// 时间管理方法
ESPTime EnergyStatisticsManager::get_current_time() const {
  if (!time_component_) {
    return {};
  }
  return time_component_->now();
}

bool EnergyStatisticsManager::is_time_valid() const {
  if (!time_component_) {
    return false;
  }
  ESPTime now = time_component_->now();
  return now.is_valid() && now.year > 2020; // 基本有效性检查
}

CompactTimeSnapshot EnergyStatisticsManager::create_time_snapshot(const ESPTime &time) const {
  CompactTimeSnapshot snapshot;
  snapshot.timestamp = time.timestamp;
  snapshot.year = time.year;
  snapshot.month = time.month;
  snapshot.day_of_month = time.day_of_month;
  snapshot.day_of_week = time.day_of_week;
  return snapshot;
}

bool EnergyStatisticsManager::is_new_day(const ESPTime &last_time, const ESPTime &current_time) const {
  return (last_time.day_of_month != current_time.day_of_month) ||
         (last_time.month != current_time.month) ||
         (last_time.year != current_time.year);
}

bool EnergyStatisticsManager::is_new_week(const ESPTime &last_time, const ESPTime &current_time) const {
  // 跨年处理
  if (last_time.year != current_time.year) {
    return true;
  }
  
  // 使用day_of_year和day_of_week精确计算周边界
  int last_week_start = last_time.day_of_year - last_time.day_of_week;
  int current_week_start = current_time.day_of_year - current_time.day_of_week;
  
  return last_week_start != current_week_start;
}

bool EnergyStatisticsManager::is_new_month(const ESPTime &last_time, const ESPTime &current_time) const {
  return (last_time.month != current_time.month) || (last_time.year != current_time.year);
}

bool EnergyStatisticsManager::is_new_year(const ESPTime &last_time, const ESPTime &current_time) const {
  return last_time.year != current_time.year;
}

// 周期处理方法
void EnergyStatisticsManager::handle_new_day() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一天");

  if (!initialized_.load()) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计管理器未初始化，无法处理新日");
    return;
  }

  // 线程安全地更新统计数据
  std::lock_guard<std::mutex> lock(*statistics_mutex_);

  // 防止并发更新
  if (unified_statistics_->updating.exchange(true)) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计数据正在更新中，跳过新日处理");
    return;
  }

  // 创建当前时刻的时间快照
  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);

  // 更新当前持久化CF_count（避免调用update_statistics_on_save造成递归）
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }

  // 更新时间点快照（所有通道共享）
  // 昨日开始 = 之前的今日开始
  unified_statistics_->period_times[0] = unified_statistics_->period_times[1]; // 昨日开始
  // 今日开始 = 当前时刻
  unified_statistics_->period_times[1] = time_snapshot; // 今日开始

  // 更新各通道的持久化CF_count快照
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    // 昨日开始CF_count = 之前的今日开始CF_count
    unified_statistics_->period_persistent_cf_count[channel][0] = unified_statistics_->period_persistent_cf_count[channel][1];
    // 今日开始CF_count = 当前持久化CF_count
    unified_statistics_->period_persistent_cf_count[channel][1] = current_persistent_cf_count_[channel].load();
  }

  unified_statistics_->last_update_timestamp = current_time.timestamp;

  // 更新完成标志
  unified_statistics_->updating.store(false);

  // 注意：这里不调用save_statistics_data_safe()，避免递归调用
  // 数据会在下次正常的save_energy_data()调用时被保存
  ESP_LOGI(ENERGY_STATS_TAG, "新日统计数据更新完成，等待下次保存周期");
}

void EnergyStatisticsManager::handle_new_week() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一周");

  if (!initialized_.load()) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计管理器未初始化，无法处理新周");
    return;
  }

  std::lock_guard<std::mutex> lock(*statistics_mutex_);

  if (unified_statistics_->updating.exchange(true)) {
    return;
  }

  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);

  // 更新当前持久化CF_count
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }

  // 更新本周开始时间
  unified_statistics_->period_times[2] = time_snapshot;

  // 更新各通道的本周开始CF_count
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->period_persistent_cf_count[channel][2] = current_persistent_cf_count_[channel].load();
  }

  unified_statistics_->last_update_timestamp = current_time.timestamp;
  unified_statistics_->updating.store(false);

  ESP_LOGI(ENERGY_STATS_TAG, "新周统计数据更新完成，等待下次保存周期");
}

void EnergyStatisticsManager::handle_new_month() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一月");

  if (!initialized_.load()) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计管理器未初始化，无法处理新月");
    return;
  }

  std::lock_guard<std::mutex> lock(*statistics_mutex_);

  if (unified_statistics_->updating.exchange(true)) {
    return;
  }

  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);

  // 更新当前持久化CF_count
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }

  // 更新本月开始时间
  unified_statistics_->period_times[3] = time_snapshot;

  // 更新各通道的本月开始CF_count
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->period_persistent_cf_count[channel][3] = current_persistent_cf_count_[channel].load();
  }

  unified_statistics_->last_update_timestamp = current_time.timestamp;
  unified_statistics_->updating.store(false);

  ESP_LOGI(ENERGY_STATS_TAG, "新月统计数据更新完成，等待下次保存周期");
}

void EnergyStatisticsManager::handle_new_year() {
  ESP_LOGI(ENERGY_STATS_TAG, "处理新的一年");

  if (!initialized_.load()) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计管理器未初始化，无法处理新年");
    return;
  }

  std::lock_guard<std::mutex> lock(*statistics_mutex_);

  if (unified_statistics_->updating.exchange(true)) {
    return;
  }

  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);

  // 更新当前持久化CF_count
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->current_persistent_cf_count[channel] = current_persistent_cf_count_[channel].load();
  }

  // 更新本年开始时间
  unified_statistics_->period_times[4] = time_snapshot;

  // 更新各通道的本年开始CF_count
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    unified_statistics_->period_persistent_cf_count[channel][4] = current_persistent_cf_count_[channel].load();
  }

  unified_statistics_->last_update_timestamp = current_time.timestamp;
  unified_statistics_->updating.store(false);

  ESP_LOGI(ENERGY_STATS_TAG, "新年统计数据更新完成，等待下次保存周期");
}

// 快照管理
void EnergyStatisticsManager::create_current_snapshot_safe() {
  if (!is_time_valid()) {
    ESP_LOGW(ENERGY_STATS_TAG, "时间无效，无法创建快照");
    return;
  }

  if (!initialized_.load() || !statistics_mutex_ || !unified_statistics_ || !current_persistent_cf_count_) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计管理器未初始化或内存未分配，无法创建快照");
    return;
  }

  std::lock_guard<std::mutex> lock(*statistics_mutex_);

  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);

  ESP_LOGI(ENERGY_STATS_TAG, "开始创建快照 - 时间: %04d-%02d-%02d %02d:%02d:%02d", 
           current_time.year, current_time.month, current_time.day_of_month,
           current_time.hour, current_time.minute, current_time.second);

  // 初始化所有周期的时间快照为当前时间
  for (int i = 0; i < 5; i++) {
    unified_statistics_->period_times[i] = time_snapshot;
  }

  // 初始化所有通道的CF_count快照为当前值
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  for (int channel = 0; channel <= max_channels; channel++) {  // +1 for sum channel
    uint32_t current_cf = current_persistent_cf_count_[channel].load();
    
    // 为避免所有周期的起始值相同导致电量差值为0，
    // 我们为非TODAY周期设置稍小的起始值，这样TODAY周期会有小的正值
    for (int period = 0; period < 5; period++) {
      if (period == 1) {  // TODAY周期使用当前值
        unified_statistics_->period_persistent_cf_count[channel][period] = current_cf;
      } else {
        // 其他周期使用当前值，这样TODAY会显示0，其他周期也显示0（刚开始时合理）
        unified_statistics_->period_persistent_cf_count[channel][period] = current_cf;
      }
    }
    unified_statistics_->current_persistent_cf_count[channel] = current_cf;
    
    ESP_LOGD(ENERGY_STATS_TAG, "通道%d快照: CF_count=%u", 
             channel < max_channels ? channel + 1 : 0, current_cf);
  }

  unified_statistics_->last_update_timestamp = current_time.timestamp;

  ESP_LOGI(ENERGY_STATS_TAG, "创建初始时间快照完成 - 共%d个通道", max_channels + 1);
}

// 电量计算方法
float EnergyStatisticsManager::calculate_energy_for_period(int channel, EnergyPeriod period) const {
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  if (channel < 0 || channel > max_channels) {  // allow sum channel
    ESP_LOGW(ENERGY_STATS_TAG, "无效通道索引: %d (最大: %d)", channel, max_channels);
    return 0.0f;
  }

  if (!initialized_.load() || !statistics_mutex_ || !unified_statistics_) {
    ESP_LOGW(ENERGY_STATS_TAG, "统计管理器未初始化或内存未分配，无法计算电量 - 初始化:%d, 互斥锁:%d, 统计数据:%d",
             initialized_.load(), !!statistics_mutex_, !!unified_statistics_);
    return 0.0f;
  }

  // 线程安全地读取统计数据
  std::lock_guard<std::mutex> lock(*statistics_mutex_);

  uint32_t start_cf_count = 0;
  uint32_t current_cf_count = unified_statistics_->current_persistent_cf_count[channel];
  const char* period_name = "";

  // 获取对应周期的起始持久化CF_count
  switch (period) {
    case EnergyPeriod::YESTERDAY:
      // 昨日电量 = (今日开始CF_count - 昨日开始CF_count) / Ke
      start_cf_count = unified_statistics_->period_persistent_cf_count[channel][0];  // 昨日开始
      current_cf_count = unified_statistics_->period_persistent_cf_count[channel][1]; // 今日开始
      period_name = "昨日";
      break;
    case EnergyPeriod::TODAY:
      start_cf_count = unified_statistics_->period_persistent_cf_count[channel][1];   // 今日开始
      period_name = "今日";
      break;
    case EnergyPeriod::THIS_WEEK:
      start_cf_count = unified_statistics_->period_persistent_cf_count[channel][2];   // 本周开始
      period_name = "本周";
      break;
    case EnergyPeriod::THIS_MONTH:
      start_cf_count = unified_statistics_->period_persistent_cf_count[channel][3];   // 本月开始
      period_name = "本月";
      break;
    case EnergyPeriod::THIS_YEAR:
      start_cf_count = unified_statistics_->period_persistent_cf_count[channel][4];   // 本年开始
      period_name = "本年";
      break;
  }
  
  // 计算CF_count差值
  uint32_t count_diff;
  if (current_cf_count >= start_cf_count) {
    count_diff = current_cf_count - start_cf_count;
  } else {
    // 处理溢出情况
    count_diff = (0xFFFFFFFF - start_cf_count) + current_cf_count + 1;
    ESP_LOGD(ENERGY_STATS_TAG, "通道%d检测到CF_count溢出: %u -> %u", 
             channel < max_channels ? channel + 1 : 0, start_cf_count, current_cf_count);
  }
  
  // 转换为电量（kWh）
  float period_energy = count_diff / esphome::bl0906_factory::Ke;
  
  // 详细的调试日志
  ESP_LOGD(ENERGY_STATS_TAG, "通道%d%s电量计算: 起始CF=%u, 当前CF=%u, 差值=%u, Ke=%.0f, 电量=%.6f kWh",
           channel < max_channels ? channel + 1 : 0, period_name, 
           start_cf_count, current_cf_count, count_diff, 
           esphome::bl0906_factory::Ke, period_energy);
  
  // 确保电量值不为负
  if (period_energy < 0.0f) {
    ESP_LOGW(ENERGY_STATS_TAG, "通道%d的%s电量为负值: %.6f，重置为0", 
             channel < max_channels ? channel + 1 : 0, period_name, period_energy);
    period_energy = 0.0f;
  }
  
  return period_energy;
}

float EnergyStatisticsManager::calculate_total_energy_for_period(EnergyPeriod period) const {
  // 总电量使用数组的最后一个元素
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  return calculate_energy_for_period(max_channels, period);
}

// 数据持久化方法
void EnergyStatisticsManager::save_statistics_data_safe() {
  // 通过parent_调用BL0906Factory的保存方法
  if (parent_) {
    parent_->save_energy_data();
  }
}

void EnergyStatisticsManager::load_statistics_data_safe() {
  // 通过parent_调用BL0906Factory的加载方法
  if (parent_) {
    parent_->load_energy_data();
  }
}

void EnergyStatisticsManager::print_sensor_status() const {
  int max_channels = parent_ ? parent_->get_max_channels() : 10;
  ESP_LOGI(ENERGY_STATS_TAG, "=== 传感器状态检查 ===");
  ESP_LOGI(ENERGY_STATS_TAG, "初始化状态: 基础=%d, 时间=%d", initialized_.load(), time_initialized_.load());
  ESP_LOGI(ENERGY_STATS_TAG, "时间组件: %p, 时间有效: %d", time_component_, is_time_valid());
  
  if (!yesterday_energy_sensors_ || !today_energy_sensors_ || !week_energy_sensors_ || 
      !month_energy_sensors_ || !year_energy_sensors_) {
    ESP_LOGW(ENERGY_STATS_TAG, "传感器数组未分配!");
    return;
  }

  int total_sensors = 0;
  for (int channel = 0; channel <= max_channels; channel++) {
    int channel_sensors = 0;
    if (yesterday_energy_sensors_[channel]) channel_sensors++;
    if (today_energy_sensors_[channel]) channel_sensors++;
    if (week_energy_sensors_[channel]) channel_sensors++;
    if (month_energy_sensors_[channel]) channel_sensors++;
    if (year_energy_sensors_[channel]) channel_sensors++;
    
    total_sensors += channel_sensors;
    
    ESP_LOGI(ENERGY_STATS_TAG, "通道%d(%d个): 昨日=%p, 今日=%p, 本周=%p, 本月=%p, 本年=%p",
             channel < max_channels ? channel + 1 : 0, channel_sensors,
             yesterday_energy_sensors_[channel], today_energy_sensors_[channel],
             week_energy_sensors_[channel], month_energy_sensors_[channel], year_energy_sensors_[channel]);
  }
  ESP_LOGI(ENERGY_STATS_TAG, "总计已注册传感器: %d个", total_sensors);
  
  // 检查统计数据状态
  if (initialized_.load() && statistics_mutex_ && unified_statistics_) {
    std::lock_guard<std::mutex> lock(*statistics_mutex_);
    ESP_LOGI(ENERGY_STATS_TAG, "=== 统计数据状态 ===");
    ESP_LOGI(ENERGY_STATS_TAG, "最后更新时间戳: %ld", unified_statistics_->last_update_timestamp);
    
    for (int channel = 0; channel <= max_channels && channel < 3; channel++) {  // 只显示前3个通道
      ESP_LOGI(ENERGY_STATS_TAG, "通道%d CF_count - 当前:%u, 昨日起始:%u, 今日起始:%u",
               channel < max_channels ? channel + 1 : 0,
               unified_statistics_->current_persistent_cf_count[channel],
               unified_statistics_->period_persistent_cf_count[channel][0],
               unified_statistics_->period_persistent_cf_count[channel][1]);
    }
  }
}

void EnergyStatisticsManager::process_pending_sensor_registrations() {
  if (pending_sensor_registrations_.empty()) {
    ESP_LOGD(ENERGY_STATS_TAG, "没有延迟的传感器注册请求需要处理");
    return;
  }

  ESP_LOGI(ENERGY_STATS_TAG, "开始处理%d个延迟的传感器注册请求", 
           static_cast<int>(pending_sensor_registrations_.size()));

  int processed_count = 0;
  for (const auto& pending_reg : pending_sensor_registrations_) {
    // 直接调用内部注册逻辑，避免递归调用
    if (!yesterday_energy_sensors_ || !today_energy_sensors_ || !week_energy_sensors_ || 
        !month_energy_sensors_ || !year_energy_sensors_) {
      ESP_LOGW(ENERGY_STATS_TAG, "传感器数组未分配，跳过延迟注册");
      continue;
    }

    int max_channels = parent_ ? parent_->get_max_channels() : 10;
    if (pending_reg.channel < 0 || pending_reg.channel > max_channels) {
      ESP_LOGW(ENERGY_STATS_TAG, "延迟注册的通道索引无效: %d", pending_reg.channel);
      continue;
    }

    switch (pending_reg.type) {
      case StatisticsSensorType::YESTERDAY_ENERGY:
      case StatisticsSensorType::YESTERDAY_TOTAL_ENERGY:
        yesterday_energy_sensors_[pending_reg.channel] = pending_reg.sensor;
        break;
      case StatisticsSensorType::TODAY_ENERGY:
      case StatisticsSensorType::TODAY_TOTAL_ENERGY:
        today_energy_sensors_[pending_reg.channel] = pending_reg.sensor;
        break;
      case StatisticsSensorType::WEEK_ENERGY:
      case StatisticsSensorType::WEEK_TOTAL_ENERGY:
        week_energy_sensors_[pending_reg.channel] = pending_reg.sensor;
        break;
      case StatisticsSensorType::MONTH_ENERGY:
      case StatisticsSensorType::MONTH_TOTAL_ENERGY:
        month_energy_sensors_[pending_reg.channel] = pending_reg.sensor;
        break;
      case StatisticsSensorType::YEAR_ENERGY:
      case StatisticsSensorType::YEAR_TOTAL_ENERGY:
        year_energy_sensors_[pending_reg.channel] = pending_reg.sensor;
        break;
    }

    processed_count++;
    ESP_LOGD(ENERGY_STATS_TAG, "延迟注册传感器成功: 类型=%d, 通道=%d", 
             static_cast<int>(pending_reg.type), pending_reg.channel);
  }

  // 清空已处理的注册请求
  pending_sensor_registrations_.clear();
  
  ESP_LOGI(ENERGY_STATS_TAG, "延迟传感器注册完成，成功处理%d个传感器", processed_count);
}

}  // namespace bl0906_factory
}  // namespace esphome 