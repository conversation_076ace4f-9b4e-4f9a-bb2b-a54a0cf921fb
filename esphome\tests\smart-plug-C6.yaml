substitutions:
  name: "smart-plug-c6"
  location: "客厅"  #放置位置

esphome:
  name: "${name}"
  name_add_mac_suffix: true
  friendly_name: "${location}智能插座C6"
  #friendly_name: "智能插座C6"
  comment: "${location}智能插座C6"
  project:
    name: carrot8848.Smart-Plug-C6
    version: "1.0"
preferences:
  flash_write_interval: 10min
esp32:
  board: esp32-c6-devkitm-1
  variant: esp32c6

# Enable logging
logger:

# Enable Home Assistant API
api:
  on_client_connected:
    - logger.log:
        format: "Client %s connected to API with IP %s"
        args: ["client_info.c_str()", "client_address.c_str()"]
    - if:
        condition:
          binary_sensor.is_on: relay_status
        then:
          - logger.log: "Power is ON!"
          - light.turn_on: stat_led
          - delay: 2s
        else:
          - logger.log: "Power is OFF!"
          - light.turn_off: stat_led
ota:
  - platform: esphome
wifi:
  use_address: ************
  # ssid: !secret wifi_ssid
  # password: !secret wifi_password
  # manual_ip:
  #   static_ip: ************
  #   gateway: *************
  #   subnet: *************
  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    password: ""
  
captive_portal:
globals:
   - id: energy_
     type: float
     restore_value: yes
   - id: energy_last  #energy from last power cycle
     type: float
     restore_value: yes
# dashboard_import:
#   package_import_url: github://carrot8848/ESPHome/Smart-Plug-01/config_v1.2.yaml@main
#   import_full_config: True
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: Sensor
      name: "Sensor"
      sorting_weight: 20
    - id: Control
      name: "Control"
      sorting_weight: 30
    - id: statistics
      name: "Energy Statistics"
      sorting_weight: 40
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 50  
    - id: diagnostic
      name: "Diagnostic"
      sorting_weight: 60  
external_components:
  - source: github://dentra/esphome-components
uart:
  id: uart_bus
  tx_pin: 4
  rx_pin: 5
  baud_rate: 4800
  stop_bits: 1
  rx_buffer_size: 512
sensor:
  - platform: bl0942
    uart_id: uart_bus
    reset: false
    voltage_reference: 15813.4320867
    current_reference: 254901.6402167
    voltage:
      name: '${name} voltage'
      accuracy_decimals: 2
      id: voltage
      icon: mdi:lightning-bolt
      web_server:
        sorting_group_id: Sensor
    current:
      name: '${name} current'
      accuracy_decimals: 2
      id: current
      icon: mdi:current-ac
      web_server:
        sorting_group_id: Sensor
    power:
      name: '${name} power'
      id: power
      accuracy_decimals: 2
      icon: mdi:flash
      filters:
        multiply: -1
      web_server:
        sorting_group_id: Sensor
    energy:
      name: '${name} energy'
      accuracy_decimals: 2
      internal: True
      id: energy
      icon: mdi:lightning-bolt-circle
      on_value: 
        then:
          - if:
              condition:
                sensor.in_range: 
                  id: energy
                  above: 0.01
              then: 
                globals.set: 
                  id: energy_
                  value: !lambda return id(energy_last) + x;
              else:
                - globals.set: 
                    id: energy_last
                    value: !lambda return id(energy_);
      
    frequency:
      name: "${name} frequency"
      accuracy_decimals: 2
      id: frequency
      icon: mdi:sine-wave
      web_server:
        sorting_group_id: Sensor
    update_interval: 10s

  - platform: template
    name: "${name} Total Energy"
    id: total_energy
    unit_of_measurement: kWh
    device_class: energy
    state_class: total_increasing
    accuracy_decimals: 2
    icon: mdi:counter
    lambda: |-
      return id(energy_);
    update_interval: 10s
    web_server:
        sorting_group_id: Sensor
  - platform: "energy_statistics"
    total: total_energy
    energy_today:
      name: "${name} Energy Today"
      icon: mdi:calendar-today
      web_server:
        sorting_group_id: statistics
    energy_yesterday:
      name: "${name} Energy Yesterday"
      icon: mdi:calendar-minus
      web_server:
        sorting_group_id: statistics
    energy_week:
      name: "${name} Energy Week"
      icon: mdi:calendar-week
      web_server:
        sorting_group_id: statistics
    energy_month:
      name: "${name} Energy Month"
      icon: mdi:calendar-month
      web_server:
        sorting_group_id: statistics

  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "${name} WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"
    icon: mdi:wifi

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "${name} WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"
    icon: mdi:wifi-strength-2
  - platform: internal_temperature
    name: "Internal Temperature"
    icon: mdi:thermometer
time:
  - platform: sntp
    servers:
      ntp.aliyun.com
    id: my_time

switch:
  # 电源继电器_开
  - platform: gpio
    name: "Power_Switch_on"
    id: relay_on
    pin: 3
    interlock: relay_off #互锁
    disabled_by_default: true #不显示UI
    icon: mdi:power-on
    on_turn_on:
    - delay: 500ms
    - switch.turn_off: relay_on
    web_server:
        sorting_group_id: Control
  # 电源继电器_关
  - platform: gpio
    name: "Power_Switch_off"    
    id: relay_off
    interlock: relay_on #互锁
    pin: 2
    disabled_by_default: true #不显示UI
    icon: mdi:power-off
    on_turn_on:
    - delay: 500ms
    - switch.turn_off: relay_off
    web_server:
        sorting_group_id: Control
  # 电源继电器_开关
  - platform: template
    name: "Power Switch"
    id: relay
    restore_mode: disabled
    icon: mdi:power
    lambda: |-
      if (id(relay_status).state) {
        return true;
      } else {
        return false;
      }
    turn_on_action:
      - switch.turn_on: relay_on
    turn_off_action:
      - switch.turn_on: relay_off
    web_server:
        sorting_group_id: Control
  - platform: restart
    name: "${name} controller Restart"
    icon: mdi:restart
    web_server:
      sorting_group_id: miscellaneous
  - platform: factory_reset
    name: Restart with Factory Default Settings
    disabled_by_default: true
    icon: mdi:factory
    web_server:
      sorting_group_id: miscellaneous

binary_sensor:
  # 电源状态(继电器状态，检测继电器输出是否有电)
  - platform: gpio
    pin: 
      number: 15
      inverted: true
      mode:
        input: true
        pullup: True
    id: relay_status
    name: "relay_status"
    device_class: power
    icon: mdi:power-plug
    on_press:
      then:
        - light.turn_on: stat_led
    on_release:
      then:
        - light.turn_off: stat_led
    web_server:
      sorting_group_id: miscellaneous
  - platform: gpio
    name: button1
    pin: 
      number: 14
      inverted: true
      mode:
        input: true
        pullup: True
    icon: mdi:gesture-tap-button
    on_click:
      then:
        - switch.toggle: relay
    web_server:
      sorting_group_id: miscellaneous
light:
  - platform: status_led
    name: "status_led"
    id: stat_led
    pin: 13
    icon: mdi:led-on
    web_server:
      sorting_group_id: miscellaneous
text_sensor:
  - platform: wifi_info
    ip_address:
      name: ${name} IP Address
      icon: mdi:ip-network
      web_server:
        sorting_group_id: diagnostic
    ssid:
      name: ${name} Connected SSID
      icon: mdi:wifi-settings
      web_server:
        sorting_group_id: diagnostic
    bssid:
      name: ${name} Connected BSSID
      icon: mdi:router-wireless
      web_server:
        sorting_group_id: diagnostic
    mac_address:
      name: ${name} Mac Wifi Address
      icon: mdi:network-outline
      web_server:
        sorting_group_id: diagnostic
    # scan_results:
    #   name: ${name} Latest Scan Results
    dns_address:
      name: ${name} DNS Address
      icon: mdi:dns
      web_server:
        sorting_group_id: diagnostic
  - platform: version
    name: "ESPHome Version"
    icon: mdi:new-box
    web_server:
        sorting_group_id: diagnostic
    