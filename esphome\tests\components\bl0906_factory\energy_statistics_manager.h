#pragma once

#include "esphome/core/component.h"
#include "esphome/core/time.h"
#include "esphome/components/time/real_time_clock.h"
#include "esphome/components/sensor/sensor.h"
#include "esphome/core/log.h"
#include <mutex>
#include <atomic>
#include <ctime>
#include <vector>

// 避免命名空间冲突
namespace esphome_time = esphome::time;

namespace esphome {
namespace bl0906_factory {

// 前向声明
class BL0906Factory;

// 前向声明枚举（定义在 bl0906_factory.h 中）
enum class StatisticsSensorType;

// 延迟传感器注册结构
struct PendingSensorRegistration {
  StatisticsSensorType type;
  sensor::Sensor *sensor;
  int channel;
};

// 电量统计周期枚举
enum class EnergyPeriod {
  YESTERDAY,
  TODAY,
  THIS_WEEK,
  THIS_MONTH,
  THIS_YEAR
};

// 紧凑的时间快照结构
struct CompactTimeSnapshot {
  time_t timestamp;       // 8字节 - Unix时间戳
  uint16_t year;         // 2字节 - 年份
  uint8_t month;         // 1字节 - 月份(1-12)
  uint8_t day_of_month;  // 1字节 - 日期(1-31)
  uint8_t day_of_week;   // 1字节 - 星期(0-6, 0=Sunday)
};

// 优化的统计数据结构 - 只在时间周期变更时写入
struct OptimizedEnergyStatistics {
  // 时间快照数据（只在周期变更时写入，大幅减少写入次数）
  CompactTimeSnapshot period_times[5]; // [0]昨日开始 [1]今日开始 [2]本周开始 [3]本月开始 [4]本年开始
  
  // 各通道的持久化CF_count快照 [通道][周期] - 只在周期变更时更新
  uint32_t period_persistent_cf_count[11][5];     // [0-9]通道+[10]总和，[0-4]对应上述5个周期
  
  // 当前持久化CF_count（运行时使用）
  uint32_t current_persistent_cf_count[11];        // [0-9]通道+[10]总和 - 当前值
  
  // 最后更新时间戳（运行时使用）
  time_t last_update_timestamp;                   // 最后更新时间
  
  uint32_t checksum;                             // 数据校验和
  
  // 移除的字段说明：
  // - current_persistent_cf_count[11]: 可以从BL0906Factory实时读取，不需要持久化
  // - last_update_timestamp: 重启后归零，不需要持久化
  // - updating: 原子标志，重启后重新初始化，不需要持久化
  
  // 线程安全标志（运行时使用，不持久化）
  std::atomic<bool> updating{false};             // 原子操作标志，防止并发更新
  
  // 自定义拷贝构造函数
  OptimizedEnergyStatistics(const OptimizedEnergyStatistics& other) {
    // 拷贝非原子成员
    for (int i = 0; i < 5; i++) {
      period_times[i] = other.period_times[i];
    }
    for (int i = 0; i < 11; i++) {
      for (int j = 0; j < 5; j++) {
        period_persistent_cf_count[i][j] = other.period_persistent_cf_count[i][j];
      }
      current_persistent_cf_count[i] = other.current_persistent_cf_count[i];
    }
    last_update_timestamp = other.last_update_timestamp;
    checksum = other.checksum;
    // 拷贝原子成员的值
    updating.store(other.updating.load());
  }
  
  // 自定义赋值操作符
  OptimizedEnergyStatistics& operator=(const OptimizedEnergyStatistics& other) {
    if (this != &other) {
      // 拷贝非原子成员
      for (int i = 0; i < 5; i++) {
        period_times[i] = other.period_times[i];
      }
      for (int i = 0; i < 11; i++) {
        for (int j = 0; j < 5; j++) {
          period_persistent_cf_count[i][j] = other.period_persistent_cf_count[i][j];
        }
        current_persistent_cf_count[i] = other.current_persistent_cf_count[i];
      }
      last_update_timestamp = other.last_update_timestamp;
      checksum = other.checksum;
      // 拷贝原子成员的值
      updating.store(other.updating.load());
    }
    return *this;
  }
  
  // 默认构造函数
  OptimizedEnergyStatistics() {
    // 初始化数组为0
    for (int i = 0; i < 5; i++) {
      period_times[i] = {};
    }
    for (int i = 0; i < 11; i++) {
      for (int j = 0; j < 5; j++) {
        period_persistent_cf_count[i][j] = 0;
      }
      current_persistent_cf_count[i] = 0;
    }
    last_update_timestamp = 0;
    checksum = 0;
    updating.store(false);
  }
};

// 电量统计管理器类 - 优化内存使用
class EnergyStatisticsManager : public PollingComponent {
public:
  EnergyStatisticsManager(BL0906Factory* parent);

  // 初始化和设置 - 采用分步初始化策略
  void setup() override;
  void basic_initialization();        // 基础初始化（内存分配，不依赖时间）
  void time_related_initialization(); // 时间相关初始化（依赖时间）
  void set_time_component(esphome_time::RealTimeClock *time_comp);
  void set_update_interval(uint32_t update_interval) { PollingComponent::set_update_interval(update_interval); }

  // 延迟初始化方法 - 在首次使用时才分配内存
  bool ensure_initialized();

  // 核心功能
  void update_persistent_cf_count(int channel, uint32_t current_cf_count);
  void update_persistent_cf_count_sum(uint32_t current_cf_count_sum);
  void check_period_changes();
  void update_statistics_on_save();      // 在持久化时更新统计数据
  void update_statistics_on_date_change();  // 在日期变更时更新统计数据

  // 按需写入功能 - 只在时间周期变更时写入，大幅减少Flash写入次数
  void save_statistics_on_period_change();  // 只在周期变更时保存统计数据
  bool is_period_change_detected();         // 检测是否有周期变更

  // PollingComponent的update方法 - 替代原来的update_sensors_on_save()
  void update() override;

  // 传感器管理
  void set_sensor(StatisticsSensorType type, sensor::Sensor *sensor, int channel = 0);

  // 线程安全的数据访问方法
  void get_statistics_data_safe(OptimizedEnergyStatistics& out_data) const;
  void set_statistics_data_safe(const OptimizedEnergyStatistics& in_data);
  
  // 调试和诊断方法
  void print_sensor_status() const;

private:
  BL0906Factory* parent_;
  esphome_time::RealTimeClock *time_component_{nullptr};

  // 延迟初始化标志
  std::atomic<bool> fully_initialized_{false};
  std::atomic<bool> setup_completed_{false};

  // 线程安全的互斥锁 - 使用智能指针延迟分配
  mutable std::unique_ptr<std::mutex> statistics_mutex_;

  // 优化的统计数据（统一管理所有通道）- 使用智能指针延迟分配
  std::unique_ptr<OptimizedEnergyStatistics> unified_statistics_;

  // 各通道统计传感器指针（动态分配，支持不同芯片的通道数）- 使用智能指针延迟分配
  std::unique_ptr<sensor::Sensor*[]> yesterday_energy_sensors_;
  std::unique_ptr<sensor::Sensor*[]> today_energy_sensors_;
  std::unique_ptr<sensor::Sensor*[]> week_energy_sensors_;
  std::unique_ptr<sensor::Sensor*[]> month_energy_sensors_;
  std::unique_ptr<sensor::Sensor*[]> year_energy_sensors_;

  // 状态管理
  std::atomic<bool> initialized_{false};        // 基础初始化状态
  std::atomic<bool> time_initialized_{false};   // 时间相关初始化状态
  std::atomic<time_t> last_check_timestamp_{0}; // 上次检查的时间戳

  // 当前持久化CF_count存储（从BL0906Factory获取）- 使用原子操作确保线程安全
  std::unique_ptr<std::atomic<uint32_t>[]> current_persistent_cf_count_;  // 当前的持久化CF_count值
  
  // 延迟传感器注册队列
  std::vector<PendingSensorRegistration> pending_sensor_registrations_;
  
  // 延迟注册处理方法
  void process_pending_sensor_registrations();
  
  // 时间管理（基于ESPTime优化）
  ESPTime get_current_time() const;
  bool is_time_valid() const;
  CompactTimeSnapshot create_time_snapshot(const ESPTime &time) const;
  bool is_new_day(const ESPTime &last_time, const ESPTime &current_time) const;
  bool is_new_week(const ESPTime &last_time, const ESPTime &current_time) const;
  bool is_new_month(const ESPTime &last_time, const ESPTime &current_time) const;
  bool is_new_year(const ESPTime &last_time, const ESPTime &current_time) const;
  
  // 周期处理
  void handle_new_day();
  void handle_new_week();
  void handle_new_month();
  void handle_new_year();
  
  // 快照管理（线程安全）
  void create_current_snapshot_safe();
  
  // 电量计算（基于持久化CF_count）
  float calculate_energy_for_period(int channel, EnergyPeriod period) const;
  float calculate_total_energy_for_period(EnergyPeriod period) const;
  
  // 数据持久化（通过parent_调用）- 线程安全版本
  void save_statistics_data_safe();
  void load_statistics_data_safe();
};

}  // namespace bl0906_factory
}  // namespace esphome 