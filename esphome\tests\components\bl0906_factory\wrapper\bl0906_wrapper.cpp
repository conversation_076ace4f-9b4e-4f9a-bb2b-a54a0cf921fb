#include "bl0906_wrapper.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "bl0906.wrapper";

// 静态实例指针
BL0906Wrapper* BL0906Wrapper::instance_ = nullptr;

void BL0906Wrapper::setup() {
  ESP_LOGD(TAG, "薄包装层初始化开始...");
  
  // 设置静态实例指针
  instance_ = this;
  
  // 检查通信适配器
  if (!comm_adapter_) {
    ESP_LOGE(TAG, "通信适配器未设置");
    this->mark_failed();
    return;
  }
  
  // 初始化通信适配器
  if (!comm_adapter_->initialize()) {
    ESP_LOGE(TAG, "通信适配器初始化失败: %s", comm_adapter_->get_last_error().c_str());
    #ifdef BL0906_FAULT_TOLERANT
    if (fault_tolerant_mode_) {
      ESP_LOGW(TAG, "容错模式：通信适配器初始化失败，但继续运行");
    } else {
      this->mark_failed();
      return;
    }
    #else
    this->mark_failed();
    return;
    #endif
  }
  
  // 设置通信回调
  bl0906_comm_callbacks_t callbacks = {
    .read_register = read_register_callback,
    .write_register = write_register_callback,
    .send_raw_command = send_raw_command_callback
  };
  
  // 确定通信类型
  bl0906_comm_type_t comm_type = BL0906_COMM_UART;
  if (comm_adapter_->get_adapter_type() == "SPI") {
    comm_type = BL0906_COMM_SPI;
  }
  
  // 初始化预编译库
  bl0906_result_t result = bl0906_core_init(chip_model_, comm_type, &callbacks);
  if (result != BL0906_SUCCESS) {
    ESP_LOGE(TAG, "预编译库初始化失败: %s", bl0906_core_get_error_string(result));
    #ifdef BL0906_FAULT_TOLERANT
    if (fault_tolerant_mode_) {
      ESP_LOGW(TAG, "容错模式：预编译库初始化失败，但继续运行");
    } else {
      this->mark_failed();
      return;
    }
    #else
    this->mark_failed();
    return;
    #endif
  }
  
  ESP_LOGI(TAG, "预编译库初始化成功，版本: %s", bl0906_core_get_version());
  
  // 加载校准数据
  if (!load_calibration_data()) {
    #ifdef BL0906_FAULT_TOLERANT
    ESP_LOGW(TAG, "容错模式：未找到校准数据，传感器精度较低");
    #else
    ESP_LOGW(TAG, "校准数据加载失败");
    #endif
  }
  
  initialized_ = true;
  ESP_LOGI(TAG, "薄包装层初始化完成");
}

void BL0906Wrapper::update() {
  if (!initialized_) {
    #ifdef BL0906_FAULT_TOLERANT
    if (fault_tolerant_mode_) {
      ESP_LOGD(TAG, "容错模式：组件未初始化，跳过更新");
      return;
    }
    #endif
    ESP_LOGW(TAG, "组件未初始化，跳过更新");
    return;
  }
  
  // 读取传感器数据并发布
  publish_sensor_data();
}

void BL0906Wrapper::dump_config() {
  ESP_LOGCONFIG(TAG, "BL0906 薄包装层:");
  ESP_LOGCONFIG(TAG, "  芯片型号: %s", chip_model_ == BL0906_CHIP_BL0906 ? "BL0906" : "BL0910");
  ESP_LOGCONFIG(TAG, "  实例ID: 0x%08X", instance_id_);
  ESP_LOGCONFIG(TAG, "  通信方式: %s", comm_adapter_ ? comm_adapter_->get_adapter_type().c_str() : "未设置");
  ESP_LOGCONFIG(TAG, "  校准状态: %s", calibration_loaded_ ? "已加载" : "未加载");
  
  #ifdef BL0906_FAULT_TOLERANT
  ESP_LOGCONFIG(TAG, "  容错模式: %s", fault_tolerant_mode_ ? "启用" : "禁用");
  #endif
  
  #ifdef BL0906_DEVELOPMENT_BUILD
  ESP_LOGCONFIG(TAG, "  构建类型: 生产版（开发功能）");
  #elif defined(BL0906_PRODUCTION_BUILD)
  ESP_LOGCONFIG(TAG, "  构建类型: 发布版（用户版本）");
  #endif
}

void BL0906Wrapper::set_chip_model(const std::string& model) {
  if (model == "bl0906") {
    chip_model_ = BL0906_CHIP_BL0906;
  } else if (model == "bl0910") {
    chip_model_ = BL0906_CHIP_BL0910;
  } else {
    ESP_LOGE(TAG, "不支持的芯片型号: %s", model.c_str());
  }
}

void BL0906Wrapper::set_communication_adapter(std::unique_ptr<CommunicationAdapterInterface> adapter) {
  comm_adapter_ = std::move(adapter);
}

void BL0906Wrapper::set_calibration_storage(std::unique_ptr<CalibrationStorageInterface> storage) {
  calibration_storage_ = std::move(storage);
}

void BL0906Wrapper::set_instance_id(uint32_t id) {
  instance_id_ = id;
}

void BL0906Wrapper::set_voltage_sensor(sensor::Sensor *sensor, int channel) {
  if (channel >= 0 && channel < 10) {
    sensors_.voltage[channel] = sensor;
  }
}

void BL0906Wrapper::set_current_sensor(sensor::Sensor *sensor, int channel) {
  if (channel >= 0 && channel < 10) {
    sensors_.current[channel] = sensor;
  }
}

void BL0906Wrapper::set_power_sensor(sensor::Sensor *sensor, int channel) {
  if (channel >= 0 && channel < 10) {
    sensors_.power[channel] = sensor;
  }
}

void BL0906Wrapper::set_energy_sensor(sensor::Sensor *sensor, int channel) {
  if (channel >= 0 && channel < 10) {
    sensors_.energy[channel] = sensor;
  }
}

void BL0906Wrapper::set_frequency_sensor(sensor::Sensor *sensor) {
  sensors_.frequency = sensor;
}

void BL0906Wrapper::set_temperature_sensor(sensor::Sensor *sensor) {
  sensors_.temperature = sensor;
}

bool BL0906Wrapper::load_calibration_data() {
  if (!calibration_storage_) {
    ESP_LOGD(TAG, "未配置校准存储，使用默认校准");
    return false;
  }
  
  // 从存储读取校准数据（ESPHome层实现）
  std::vector<CalibrationEntry> entries;
  if (!calibration_storage_->read_instance(instance_id_, entries)) {
    ESP_LOGD(TAG, "未找到校准数据");
    return false;
  }
  
  // 转换为预编译库格式
  calibration_entries_.clear();
  for (const auto& entry : entries) {
    bl0906_calibration_entry_t core_entry;
    core_entry.register_addr = entry.register_addr;
    core_entry.value = entry.value;
    calibration_entries_.push_back(core_entry);
  }
  
  // 应用校准数据到预编译库
  bl0906_result_t result = bl0906_core_apply_calibration(
    calibration_entries_.data(), 
    calibration_entries_.size()
  );
  
  if (result == BL0906_SUCCESS) {
    calibration_loaded_ = true;
    ESP_LOGI(TAG, "校准数据加载成功，共%d条", calibration_entries_.size());
    return true;
  } else {
    ESP_LOGE(TAG, "校准数据应用失败: %s", bl0906_core_get_error_string(result));
    return false;
  }
}

void BL0906Wrapper::publish_sensor_data() {
  const int max_channels = (chip_model_ == BL0906_CHIP_BL0906) ? 6 : 10;
  
  for (int channel = 0; channel <= max_channels; ++channel) {
    if (!read_channel_data(channel)) {
      handle_communication_error("读取通道数据");
      continue;
    }
  }
  
  #ifdef BL0906_FAULT_TOLERANT
  if (fault_tolerant_mode_ && !calibration_loaded_) {
    // 发布版特色：每次都提醒用户校准数据缺失
    ESP_LOGW(TAG, "未找到校准数据，传感器精度较低，建议进行校准");
  }
  #endif
}

bool BL0906Wrapper::read_channel_data(int channel) {
  bl0906_sensor_data_t data;
  bl0906_result_t result = bl0906_core_read_sensor_data(channel, &data);
  
  if (result != BL0906_SUCCESS) {
    ESP_LOGD(TAG, "读取通道%d数据失败: %s", channel, bl0906_core_get_error_string(result));
    return false;
  }
  
  if (!data.valid) {
    ESP_LOGD(TAG, "通道%d数据无效", channel);
    return false;
  }
  
  // 发布传感器数据
  if (sensors_.voltage[channel] && !isnan(data.voltage)) {
    sensors_.voltage[channel]->publish_state(data.voltage);
  }
  
  if (sensors_.current[channel] && !isnan(data.current)) {
    sensors_.current[channel]->publish_state(data.current);
  }
  
  if (sensors_.power[channel] && !isnan(data.power)) {
    sensors_.power[channel]->publish_state(data.power);
  }
  
  if (sensors_.energy[channel] && !isnan(data.energy)) {
    sensors_.energy[channel]->publish_state(data.energy);
  }
  
  // 频率和温度只在总通道（channel 0）发布
  if (channel == 0) {
    if (sensors_.frequency && !isnan(data.frequency)) {
      sensors_.frequency->publish_state(data.frequency);
    }
    
    if (sensors_.temperature && !isnan(data.temperature)) {
      sensors_.temperature->publish_state(data.temperature);
    }
  }
  
  #ifdef BL0906_FAULT_TOLERANT
  if (fault_tolerant_mode_) {
    last_successful_read_ = millis();
    error_count_ = 0;  // 重置错误计数
  }
  #endif
  
  return true;
}

void BL0906Wrapper::handle_communication_error(const char* operation) {
  #ifdef BL0906_FAULT_TOLERANT
  if (fault_tolerant_mode_) {
    error_count_++;
    ESP_LOGW(TAG, "容错模式：%s失败（错误计数：%d）", operation, error_count_);
    
    // 如果错误过多，可以考虑重新初始化
    if (error_count_ > 10) {
      ESP_LOGW(TAG, "容错模式：连续错误过多，尝试重新初始化通信");
      if (comm_adapter_) {
        comm_adapter_->initialize();
      }
      error_count_ = 0;
    }
    return;
  }
  #endif
  
  ESP_LOGE(TAG, "%s失败", operation);
}

#ifdef BL0906_DEVELOPMENT_BUILD
bool BL0906Wrapper::write_calibration_register(uint8_t reg, int16_t value) {
  bl0906_calibration_entry_t entry = {.register_addr = reg, .value = value};
  bl0906_result_t result = bl0906_core_apply_calibration(&entry, 1);
  return result == BL0906_SUCCESS;
}

bool BL0906Wrapper::calculate_calibration_coefficient(uint8_t reg, int32_t raw_value, float reference, int16_t* coeff) {
  bl0906_result_t result = bl0906_core_calculate_calibration(reg, raw_value, reference, coeff);
  return result == BL0906_SUCCESS;
}
#endif

// 静态回调函数实现
bl0906_result_t BL0906Wrapper::read_register_callback(uint8_t address, int32_t* value) {
  if (!instance_ || !instance_->comm_adapter_) {
    return BL0906_ERROR_COMMUNICATION;
  }
  
  bool success = false;
  *value = instance_->comm_adapter_->read_register(address, &success);
  return success ? BL0906_SUCCESS : BL0906_ERROR_COMMUNICATION;
}

bl0906_result_t BL0906Wrapper::write_register_callback(uint8_t address, int16_t value) {
  if (!instance_ || !instance_->comm_adapter_) {
    return BL0906_ERROR_COMMUNICATION;
  }
  
  bool success = instance_->comm_adapter_->write_register(address, value);
  return success ? BL0906_SUCCESS : BL0906_ERROR_COMMUNICATION;
}

bl0906_result_t BL0906Wrapper::send_raw_command_callback(const uint8_t* data, size_t length) {
  if (!instance_ || !instance_->comm_adapter_) {
    return BL0906_ERROR_COMMUNICATION;
  }
  
  bool success = instance_->comm_adapter_->send_raw_command(data, length);
  return success ? BL0906_SUCCESS : BL0906_ERROR_COMMUNICATION;
}

} // namespace bl0906_factory
} // namespace esphome 