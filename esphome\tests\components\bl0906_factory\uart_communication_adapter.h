#pragma once

#ifdef USE_UART_COMMUNICATION_ADAPTER

#include "communication_adapter_base.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/uart/uart_component.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include <vector>
#include <functional>

namespace esphome {
namespace bl0906_factory {

/**
 * UART通信适配器实现
 * 
 * 实现基于UART的BL0906通信协议
 */
class UartCommunicationAdapter : public CommunicationAdapterBase, public uart::UARTDevice {
public:
  UartCommunicationAdapter() = default;
  explicit UartCommunicationAdapter(uart::UARTComponent *parent) : uart::UARTDevice(parent) {}
  ~UartCommunicationAdapter() override = default;
  
  // ========== 设置方法 ==========
  
  // 注意：移除了set_uart_parent方法，因为现在直接在构造函数中设置parent
  
  // ========== CommunicationAdapterInterface 实现 ==========
  
  bool initialize() override;
  int32_t read_register(uint8_t address, bool* success = nullptr) override;
  bool write_register(uint8_t address, int16_t value) override;
  bool send_raw_command(const uint8_t* command, size_t length) override;
  
  bool is_available() override;
  bool is_connected() override;
  void flush_buffer() override;
  
  std::string get_adapter_type() const override;
  std::string get_status_info() const override;
  bool self_test() override;

private:
  // ========== 内部数据结构 ==========
  
  /**
   * UART数据包结构体
   */
  struct DataPacket {
    uint8_t l;            // low byte
    uint8_t m;            // middle byte
    uint8_t h;            // high byte
    uint8_t checksum;     // checksum
  };
  
  /**
   * BL0906 24位无符号数据类型
   */
  struct ube24_t {
    uint8_t l;            // low byte
    uint8_t m;            // middle byte
    uint8_t h;            // high byte
  };
  
  /**
   * BL0906 24位有符号数据类型
   */
  struct sbe24_t {
    uint8_t l;            // low byte
    uint8_t m;            // middle byte
    int8_t h;             // high byte, signed for proper sign extension
  };
  
  // ========== 内部状态 ==========
  
  // ========== 配置参数 ==========
  
  static constexpr uint32_t UART_TIMEOUT_MS = 200;
  static constexpr size_t MAX_RESPONSE_LENGTH = 6;
  
  // ========== 内部方法 ==========
  
  /**
   * 等待UART数据可用
   * @param len 期望的数据长度
   * @param timeout_ms 超时时间（毫秒）
   * @return true 数据可用，false 超时
   */
  bool wait_until_available(size_t len, uint32_t timeout_ms);
  
  /**
   * 发送读取命令并接收响应
   * @param address 寄存器地址
   * @param success 成功标志指针
   * @return 读取到的值
   */
  int32_t send_read_command_and_receive(uint8_t address, bool* success);
  
  /**
   * 发送写入命令
   * @param address 寄存器地址
   * @param value 要写入的值
   * @return true 写入成功，false 写入失败
   */
  bool send_write_command(uint8_t address, int16_t value);
  
  /**
   * 计算校验和
   * @param data 数据指针
   * @param len 数据长度
   * @return 校验和
   */
  uint8_t calculate_checksum(const uint8_t* data, size_t len);
  
  /**
   * 验证响应数据的校验和
   * @param data 响应数据
   * @return true 校验和正确，false 校验和错误
   */
  bool verify_checksum(const std::vector<uint8_t>& data);
  
  /**
   * 转换24位数据为32位整数（无符号）
   * @param data 24位数据
   * @return 32位整数值
   */
  uint32_t to_uint32_t(const ube24_t& data);
  
  /**
   * 转换24位数据为32位整数（有符号）
   * @param data 24位数据
   * @return 32位整数值
   */
  int32_t to_int32_t(const sbe24_t& data);
  
  // 寄存器类型判断函数已移至 bl0906_registers.h 作为内联函数
  // 使用 esphome::bl0906_factory::is_16bit_register() 和 is_unsigned_register()
  
  // 重复方法已移至基类
};

} // namespace bl0906_factory
} // namespace esphome

#endif // USE_UART_COMMUNICATION_ADAPTER 