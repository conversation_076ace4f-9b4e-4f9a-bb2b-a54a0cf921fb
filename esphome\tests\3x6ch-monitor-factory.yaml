substitutions:
  name: "3x6-ch-monitor"
  friendly_name: "3x6-ch-monitor"
  chA1: "ch_A1"   #可根据需要修改名称
  chA2: "ch_A2"   #可根据需要修改名称
  chA3: "ch_A3"    #可根据需要修改名称
  chA4: "ch_A4"    #可根据需要修改名称
  chA5: "ch_A5"  #可根据需要修改名称
  chA6: "ch_A6"     #可根据需要修改名称
  chB1: "ch_B1"   #可根据需要修改名称
  chB2: "ch_B2"   #可根据需要修改名称
  chB3: "ch_B3"    #可根据需要修改名称
  chB4: "ch_B4"    #可根据需要修改名称
  chB5: "ch_B5"  #可根据需要修改名称
  chB6: "ch_B6"     #可根据需要修改名称
  chC1: "ch_C1"   #可根据需要修改名称
  chC2: "ch_C2"   #可根据需要修改名称
  chC3: "ch_C3"    #可根据需要修改名称
  chC4: "ch_C4"    #可根据需要修改名称
  chC5: "ch_C5"  #可根据需要修改名称
  chC6: "ch_C6"     #可根据需要修改名称
esphome:
  name: "${name}"
  friendly_name: "${friendly_name}"
preferences:
  flash_write_interval: 20min
esp32:
  board: esp32dev
  framework:
    type: arduino

# Enable logging
logger:
  baud_rate: 0
  level: Info
  logs:
    sensor:094: none
# Enable Home Assistant API
api:

ota:
  - platform: esphome

wifi:
  # ssid: !secret wifi_ssid
  # password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "3X6Ch-Monitor"
    password: ""

captive_portal:
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: power_status
      name: "Power Status"
      sorting_weight: 10
    - id: currentA
      name: "CurrentA"
      sorting_weight: 20
    - id: powerA
      name: "PowerA"
      sorting_weight: 30
    - id: energyA
      name: "EnergyA"
      sorting_weight: 40
    - id: currentB
      name: "CurrentB"
      sorting_weight: 21
    - id: powerB
      name: "PowerB"
      sorting_weight: 31
    - id: energyB
      name: "EnergyB"
      sorting_weight: 41
    - id: currentC
      name: "CurrentC"
      sorting_weight: 22
    - id: powerC
      name: "PowerC"
      sorting_weight: 32
    - id: energyC
      name: "EnergyC"
      sorting_weight: 42
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 50
  
external_components:
  - source: my_components

status_led:
  pin: GPIO4
uart:
  - id: uart_bus_a
    rx_pin: 25
    tx_pin: 14
    baud_rate: 19200
  - id: uart_bus_b
    rx_pin: 33
    tx_pin: 27
    baud_rate: 19200
  - id: uart_bus_c
    rx_pin: 32
    tx_pin: 26
    baud_rate: 19200
bl0906_factory:
  - id: sensor_bl0906_a
    uart_id: uart_bus_a
    update_interval: 10s
    instance_id: 0x906B0001  # 手动指定实例ID
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: eeprom
  - id: sensor_bl0906_b
    uart_id: uart_bus_b
    update_interval: 10s
    instance_id: 0x906B0002  # 手动指定实例ID
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: eeprom
  - id: sensor_bl0906_c
    uart_id: uart_bus_c
    update_interval: 10s
    instance_id: 0x906B0003  # 手动指定实例ID
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: eeprom
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_a
    frequency:
      name: 'A Phase Frequency'
      icon: "mdi:sine-wave"
      web_server:
        sorting_group_id: power_status 
    temperature:
      name: 'A Phase BL0906 Temperature'
      icon: "mdi:thermometer"
      web_server:
          sorting_group_id: miscellaneous
    voltage:
      name: 'A Phase Voltage'
      icon: "mdi:lightning-bolt"
      web_server:
        sorting_group_id: power_status 
    power_sum:
      name: "A Phase sum power"
      icon: "mdi:sigma"
      web_server:
        sorting_group_id: powerA
    energy_sum: 
      id: chsA_energy_sum
      name: "A phase sum energy"
    
    # A相通道1配置
    ch1:
      current:
        name: "${chA1} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentA
      power:
        name: "${chA1} power"
        id: chA1_power
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerA
      energy:
        name: "${chA1} energy"
        id: chA1_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyA
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # A相通道2配置
    ch2:
      current:
        name: "${chA2} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentA
      power:
        name: "${chA2} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerA
      energy:
        name: "${chA2} energy"
        id: chA2_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyA
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # A相通道3配置
    ch3:
      current:
        name: "${chA3} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentA
      power:
        name: "${chA3} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerA
      energy:
        name: "${chA3} energy"
        id: chA3_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyA
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # A相通道4配置
    ch4:
      current:
        name: "${chA4} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentA
      power:
        name: "${chA4} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerA
      energy:
        name: "${chA4} energy"
        id: chA4_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyA
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # A相通道5配置
    ch5:
      current:
        name: "${chA5} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentA
      power:
        name: "${chA5} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerA
      energy:
        name: "${chA5} energy"
        id: chA5_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyA
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # A相通道6配置
    ch6:
      current:
        name: "${chA6} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentA
      power:
        name: "${chA6} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerA
      energy:
        name: "${chA6} energy"
        id: chA6_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyA
        accuracy_decimals: 6
        unit_of_measurement: kWh 

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_b
    frequency:
      name: 'B Phase Frequency'
      icon: "mdi:sine-wave"
      web_server:
        sorting_group_id: power_status 
    temperature:
      name: 'B Phase BL0906 Temperature'
      icon: "mdi:thermometer"
      web_server:
          sorting_group_id: miscellaneous
    voltage:
      name: 'B Phase Voltage'
      icon: "mdi:lightning-bolt"
      web_server:
        sorting_group_id: power_status 
    power_sum:
      name: "B Phase sum power"
      icon: "mdi:sigma"
      web_server:
        sorting_group_id: powerB
    energy_sum: 
      id: chsB_energy_sum
      name: "B phase sum energy"
    
    # B相通道1配置
    ch1:
      current:
        name: "${chB1} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentB
      power:
        name: "${chB1} power"
        id: chB1_power
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerB
      energy:
        name: "${chB1} energy"
        id: chB1_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyB
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # B相通道2配置
    ch2:
      current:
        name: "${chB2} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentB
      power:
        name: "${chB2} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerB
      energy:
        name: "${chB2} energy"
        id: chB2_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyB
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # B相通道3配置
    ch3:
      current:
        name: "${chB3} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentB
      power:
        name: "${chB3} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerB
      energy:
        name: "${chB3} energy"
        id: chB3_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyB
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # B相通道4配置
    ch4:
      current:
        name: "${chB4} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentB
      power:
        name: "${chB4} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerB
      energy:
        name: "${chB4} energy"
        id: chB4_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyB
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # B相通道5配置
    ch5:
      current:
        name: "${chB5} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentB
      power:
        name: "${chB5} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerB
      energy:
        name: "${chB5} energy"
        id: chB5_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyB
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # B相通道6配置
    ch6:
      current:
        name: "${chB6} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentB
      power:
        name: "${chB6} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerB
      energy:
        name: "${chB6} energy"
        id: chB6_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyB
        accuracy_decimals: 6
        unit_of_measurement: kWh 

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_c
    frequency:
      name: 'C Phase Frequency'
      icon: "mdi:sine-wave"
      web_server:
        sorting_group_id: power_status 
    temperature:
      name: 'C Phase BL0906 Temperature'
      icon: "mdi:thermometer"
      web_server:
          sorting_group_id: miscellaneous
    voltage:
      name: 'C Phase Voltage'
      icon: "mdi:lightning-bolt"
      web_server:
        sorting_group_id: power_status 
    power_sum:
      name: "C Phase sum power"
      icon: "mdi:sigma"
      web_server:
        sorting_group_id: powerC
    energy_sum: 
      id: chsC_energy_sum
      name: "C phase sum energy"
    
    # C相通道1配置
    ch1:
      current:
        name: "${chC1} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentC
      power:
        name: "${chC1} power"
        id: chC1_power
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerC
      energy:
        name: "${chC1} energy"
        id: chC1_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyC
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # C相通道2配置
    ch2:
      current:
        name: "${chC2} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentC
      power:
        name: "${chC2} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerC
      energy:
        name: "${chC2} energy"
        id: chC2_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyC
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # C相通道3配置
    ch3:
      current:
        name: "${chC3} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentC
      power:
        name: "${chC3} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerC
      energy:
        name: "${chC3} energy"
        id: chC3_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyC
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # C相通道4配置
    ch4:
      current:
        name: "${chC4} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentC
      power:
        name: "${chC4} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerC
      energy:
        name: "${chC4} energy"
        id: chC4_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyC
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # C相通道5配置
    ch5:
      current:
        name: "${chC5} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentC
      power:
        name: "${chC5} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerC
      energy:
        name: "${chC5} energy"
        id: chC5_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyC
        accuracy_decimals: 6
        unit_of_measurement: kWh

    # C相通道6配置
    ch6:
      current:
        name: "${chC6} current"
        icon: "mdi:current-ac"
        web_server:
          sorting_group_id: currentC
      power:
        name: "${chC6} power"
        icon: "mdi:power-plug"
        web_server:
          sorting_group_id: powerC
      energy:
        name: "${chC6} energy"
        id: chC6_energy
        icon: "mdi:lightning-bolt"
        web_server:
          sorting_group_id: energyC
        accuracy_decimals: 6
        unit_of_measurement: kWh 

  - platform: template
    name: "A1+B1+C1 Total Power"
    id: ch1_total_power
    unit_of_measurement: "W"
    accuracy_decimals: 2
    lambda: |-
      return id(chA1_power).state + id(chB1_power).state+id(chC1_power).state;
    update_interval: 10s
    icon: "mdi:lightning-bolt"
    web_server:
        sorting_group_id: power_status 
  - platform: template
    name: "A1+B1+C1 Total Energy"
    id: ch1_total_energy
    unit_of_measurement: kWh
    accuracy_decimals: 2
    lambda: |-
      return id(chA1_energy).state + id(chB1_energy).state+id(chC1_energy).state;
    update_interval: 10s
    device_class: energy
    state_class: total_increasing
    icon: "mdi:home-lightning-bolt"
    web_server:
        sorting_group_id: power_status 

  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"
    icon: "mdi:wifi-strength-2"

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"
    icon: "mdi:wifi"


time:
  - platform: sntp
    id: my_time
    servers: ntp.aliyun.com
text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
      icon: "mdi:ip-network"
    ssid:
      name: Energy_meter Connected SSID
      icon: "mdi:wifi-settings"
    bssid:
      name: Energy_meter Connected BSSID
      icon: "mdi:access-point-network"
    mac_address:
      name: Energy_meter Mac Wifi Address
      icon: "mdi:network"
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address
      icon: "mdi:dns"
  - platform: template
    name: "System Info"
    icon: "mdi:chip"
    lambda: |-
      return {"Uptime: " + to_string(millis() / 1000) + "s, Free Heap: " + to_string(esp_get_free_heap_size()) + " bytes"};
    update_interval: 60s


switch:
  - platform: restart
    name: "${name} controller Restart"
    icon: "mdi:restart"
  - platform: factory_reset
    name: Restart with Factory Default Settings
    icon: "mdi:restart-alert"
  - platform: template
    name: "Clear User Data"
    switches:
      factory_reset:
        on_turn_on:
          lambda: |-
            // 仅清除用户设置，保留校准数据
            ESP_LOGI("RESET", "Erasing user data...");
            esphome::preferences::global_preferences->clear();  // 清除通用偏好
  - platform: template
    name: "BL0906 A update switch"
    optimistic: true
    assumed_state: true
    turn_off_action:
      - logger.log: "turn off bl0906 A update"
      - component.suspend: sensor_bl0906_a
    turn_on_action:
      - logger.log: "turn on bl0906 A update"
      - component.resume: sensor_bl0906_a

  - platform: template
    name: "BL0906 B update switch"
    optimistic: true
    assumed_state: true
    turn_off_action:
      - logger.log: "turn off bl0906 B update"
      - component.suspend: sensor_bl0906_b
    turn_on_action:
      - logger.log: "turn on bl0906 B update"
      - component.resume: sensor_bl0906_b

  - platform: template
    name: "BL0906 C update switch"
    optimistic: true
    assumed_state: true
    turn_off_action:
      - logger.log: "turn off bl0906 C update"
      - component.suspend: sensor_bl0906_c
    turn_on_action:
      - logger.log: "turn on bl0906 C update"
      - component.resume: sensor_bl0906_c

  # 电量持久化开关
  - platform: template
    name: "Energy Persistence A"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "A相电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "A相电量持久化存储已禁用");

  - platform: template
    name: "Energy Persistence B"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "B相电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "B相电量持久化存储已禁用");

  - platform: template
    name: "Energy Persistence C"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "C相电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "C相电量持久化存储已禁用");

number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_a
    chgn_decimal_1:
      name: "A Phase CH_1 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentA
    chgn_decimal_2:
      name: "A Phase CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentA
    chgn_decimal_3:
      name: "A Phase CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentA
    chgn_decimal_4:
      name: "A Phase CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentA
    chgn_decimal_5:
      name: "A Phase CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentA
    chgn_decimal_6:
      name: "A Phase CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentA
    chgn_v_decimal:
      name: "A Phase Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: powerA
    chos_decimal_1:
      name: "A Phase CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentA
    chos_decimal_2:
      name: "A Phase CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentA
    chos_decimal_3:
      name: "A Phase CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentA
    chos_decimal_4:
      name: "A Phase CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentA
    chos_decimal_5:
      name: "A Phase CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentA
    chos_decimal_6:
      name: "A Phase CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentA
    chos_v_decimal:
      name: "A Phase Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: powerA
    rmsgn_decimal_1:
      name: "A Phase CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsgn_decimal_2:
      name: "A Phase CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsgn_decimal_3:
      name: "A Phase CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsgn_decimal_4:
      name: "A Phase CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsgn_decimal_5:
      name: "A Phase CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsgn_decimal_6:
      name: "A Phase CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsos_decimal_1:
      name: "A Phase CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsos_decimal_2:
      name: "A Phase CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsos_decimal_3:
      name: "A Phase CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsos_decimal_4:
      name: "A Phase CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsos_decimal_5:
      name: "A Phase CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentA
    rmsos_decimal_6:
      name: "A Phase CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentA

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_b
    chgn_decimal_1:
      name: "B Phase CH_1 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentB
    chgn_decimal_2:
      name: "B Phase CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentB
    chgn_decimal_3:
      name: "B Phase CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentB
    chgn_decimal_4:
      name: "B Phase CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentB
    chgn_decimal_5:
      name: "B Phase CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentB
    chgn_decimal_6:
      name: "B Phase CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentB
    chgn_v_decimal:
      name: "B Phase Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: powerB
    chos_decimal_1:
      name: "B Phase CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentB
    chos_decimal_2:
      name: "B Phase CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentB
    chos_decimal_3:
      name: "B Phase CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentB
    chos_decimal_4:
      name: "B Phase CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentB
    chos_decimal_5:
      name: "B Phase CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentB
    chos_decimal_6:
      name: "B Phase CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentB
    chos_v_decimal:
      name: "B Phase Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: powerB
    rmsgn_decimal_1:
      name: "B Phase CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsgn_decimal_2:
      name: "B Phase CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsgn_decimal_3:
      name: "B Phase CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsgn_decimal_4:
      name: "B Phase CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsgn_decimal_5:
      name: "B Phase CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsgn_decimal_6:
      name: "B Phase CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsos_decimal_1:
      name: "B Phase CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsos_decimal_2:
      name: "B Phase CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsos_decimal_3:
      name: "B Phase CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsos_decimal_4:
      name: "B Phase CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsos_decimal_5:
      name: "B Phase CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentB
    rmsos_decimal_6:
      name: "B Phase CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentB

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_c
    chgn_decimal_1:
      name: "C Phase CH_1 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentC
    chgn_decimal_2:
      name: "C Phase CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentC
    chgn_decimal_3:
      name: "C Phase CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentC
    chgn_decimal_4:
      name: "C Phase CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentC
    chgn_decimal_5:
      name: "C Phase CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentC
    chgn_decimal_6:
      name: "C Phase CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: currentC
    chgn_v_decimal:
      name: "C Phase Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: powerC
    chos_decimal_1:
      name: "C Phase CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentC
    chos_decimal_2:
      name: "C Phase CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentC
    chos_decimal_3:
      name: "C Phase CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentC
    chos_decimal_4:
      name: "C Phase CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentC
    chos_decimal_5:
      name: "C Phase CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentC
    chos_decimal_6:
      name: "C Phase CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: currentC
    chos_v_decimal:
      name: "C Phase Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: powerC
    rmsgn_decimal_1:
      name: "C Phase CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsgn_decimal_2:
      name: "C Phase CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsgn_decimal_3:
      name: "C Phase CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsgn_decimal_4:
      name: "C Phase CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsgn_decimal_5:
      name: "C Phase CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsgn_decimal_6:
      name: "C Phase CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsos_decimal_1:
      name: "C Phase CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsos_decimal_2:
      name: "C Phase CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsos_decimal_3:
      name: "C Phase CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsos_decimal_4:
      name: "C Phase CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsos_decimal_5:
      name: "C Phase CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentC
    rmsos_decimal_6:
      name: "C Phase CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: currentC

button:
  - platform: template
    name: "读取校正值A"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
            bl0906->refresh_all_calib_numbers();

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy A"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data A"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data A"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence A"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
      
  - platform: template
    name: "CALCULATE RMSOS A"
    on_press:
      - lambda: |-
          auto bl0906 = id(sensor_bl0906_a);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

  - platform: template
    name: "Save Calibration Data to Flash A"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
        
  - platform: template  
    name: "Verify Calibration Data A"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_a));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          // 触发重新读取校准寄存器
          bl0906->refresh_all_calib_numbers();
          }

#B相
  - platform: template
    name: "读取校正值B"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
            bl0906->refresh_all_calib_numbers();

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy B"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data B"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data B"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence B"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
      
  - platform: template
    name: "CALCULATE RMSOS B"
    on_press:
      - lambda: |-
          auto bl0906 = id(sensor_bl0906_b);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

  - platform: template
    name: "Save Calibration Data to Flash B"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
        
  - platform: template  
    name: "Verify Calibration Data B"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_b));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          // 触发重新读取校准寄存器
          bl0906->refresh_all_calib_numbers();
          }

#C相
  - platform: template
    name: "读取校正值C"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
            bl0906->refresh_all_calib_numbers();

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy C"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data C"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data C"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence C"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
      
  - platform: template
    name: "CALCULATE RMSOS C"
    on_press:
      - lambda: |-
          auto bl0906 = id(sensor_bl0906_c);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

  - platform: template
    name: "Save Calibration Data to Flash C"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
        
  - platform: template  
    name: "Verify Calibration Data C"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906_c));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          // 触发重新读取校准寄存器
          bl0906->refresh_all_calib_numbers();
          }