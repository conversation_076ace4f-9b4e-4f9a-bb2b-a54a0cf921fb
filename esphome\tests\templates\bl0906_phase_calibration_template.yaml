# BL0906相位校准参数模板
# 用于生成单个相位的所有校准参数number组件
# 使用方法:
# packages:
#   phase_a_calibration: !include
#     file: templates/bl0906_phase_calibration_template.yaml
#     vars:
#       phase_name: "A"
#       phase_lower: "a"
#       current_group: "currentA"
#       power_group: "powerA"

defaults:
  phase_name: "A"
  phase_lower: "a"
  current_group: "currentA"
  power_group: "powerA"

number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906_${phase_lower}
    
    # 电流增益参数
    chgn_decimal_1:
      name: "${phase_name} Phase CH_1 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chgn_decimal_2:
      name: "${phase_name} Phase CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chgn_decimal_3:
      name: "${phase_name} Phase CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chgn_decimal_4:
      name: "${phase_name} Phase CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chgn_decimal_5:
      name: "${phase_name} Phase CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chgn_decimal_6:
      name: "${phase_name} Phase CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chgn_v_decimal:
      name: "${phase_name} Phase Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: voltage_status
    
    # 电流偏移参数
    chos_decimal_1:
      name: "${phase_name} Phase CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chos_decimal_2:
      name: "${phase_name} Phase CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chos_decimal_3:
      name: "${phase_name} Phase CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chos_decimal_4:
      name: "${phase_name} Phase CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chos_decimal_5:
      name: "${phase_name} Phase CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chos_decimal_6:
      name: "${phase_name} Phase CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    chos_v_decimal:
      name: "${phase_name} Phase Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: voltage_status
    
    # RMS增益参数
    rmsgn_decimal_1:
      name: "${phase_name} Phase CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsgn_decimal_2:
      name: "${phase_name} Phase CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsgn_decimal_3:
      name: "${phase_name} Phase CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsgn_decimal_4:
      name: "${phase_name} Phase CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsgn_decimal_5:
      name: "${phase_name} Phase CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsgn_decimal_6:
      name: "${phase_name} Phase CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    
    # RMS偏移参数
    rmsos_decimal_1:
      name: "${phase_name} Phase CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsos_decimal_2:
      name: "${phase_name} Phase CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsos_decimal_3:
      name: "${phase_name} Phase CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsos_decimal_4:
      name: "${phase_name} Phase CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsos_decimal_5:
      name: "${phase_name} Phase CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: ${current_group}
    rmsos_decimal_6:
      name: "${phase_name} Phase CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: ${current_group} 