#!/bin/bash

# BL0906 Factory 预编译库构建脚本
# 此脚本将核心算法编译为预编译库，保护关键IP

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="bl0906_factory"
CORE_LIB_NAME="libbl0906_core.a"
VERSION="2.0.0"

# 目录配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
BUILD_DIR="$PROJECT_DIR/build"
RELEASE_DIR="$PROJECT_DIR/release"
TEMP_DIR="$PROJECT_DIR/temp"

# 源文件配置
CORE_SOURCES=(
    "bl0906_core_impl.cpp"
)

HEADER_FILES=(
    "bl0906_core_api.h"
)

# 编译器配置
CC="xtensa-esp32-elf-gcc"
CXX="xtensa-esp32-elf-g++"
AR="xtensa-esp32-elf-ar"
STRIP="xtensa-esp32-elf-strip"

# 编译选项
CFLAGS="-Os -g0 -DNDEBUG -ffunction-sections -fdata-sections -fstrict-aliasing -mlongcalls"
CXXFLAGS="$CFLAGS -std=c++17 -fno-rtti -fno-exceptions"
INCLUDES="-I. -I$PROJECT_DIR"

# 函数定义
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}  BL0906 Factory 预编译库构建工具 v$VERSION${NC}"
    echo -e "${BLUE}================================================${NC}"
    echo
}

print_step() {
    echo -e "${GREEN}[步骤]${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[信息]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

check_dependencies() {
    print_step "检查构建依赖..."
    
    # 检查编译器
    if ! command -v $CC &> /dev/null; then
        print_error "未找到 $CC 编译器"
        print_info "请安装 ESP-IDF 开发环境"
        exit 1
    fi
    
    if ! command -v $CXX &> /dev/null; then
        print_error "未找到 $CXX 编译器"
        print_info "请安装 ESP-IDF 开发环境"
        exit 1
    fi
    
    if ! command -v $AR &> /dev/null; then
        print_error "未找到 $AR 归档工具"
        print_info "请安装 ESP-IDF 开发环境"
        exit 1
    fi
    
    print_success "所有依赖检查通过"
}

prepare_directories() {
    print_step "准备构建目录..."
    
    # 清理旧的构建目录
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    # 创建目录
    mkdir -p "$BUILD_DIR"
    mkdir -p "$RELEASE_DIR"
    mkdir -p "$TEMP_DIR"
    
    print_success "目录准备完成"
}

validate_sources() {
    print_step "验证源文件..."
    
    for source in "${CORE_SOURCES[@]}"; do
        if [ ! -f "$PROJECT_DIR/$source" ]; then
            print_error "源文件不存在: $source"
            exit 1
        fi
        print_info "✓ $source"
    done
    
    for header in "${HEADER_FILES[@]}"; do
        if [ ! -f "$PROJECT_DIR/$header" ]; then
            print_error "头文件不存在: $header"
            exit 1
        fi
        print_info "✓ $header"
    done
    
    print_success "源文件验证完成"
}

compile_core_library() {
    print_step "编译核心库..."
    
    cd "$BUILD_DIR"
    
    # 编译所有源文件
    OBJECT_FILES=()
    for source in "${CORE_SOURCES[@]}"; do
        source_path="$PROJECT_DIR/$source"
        object_file="${source%.*}.o"
        
        print_info "编译 $source..."
        
        # 编译C++源文件
        $CXX $CXXFLAGS $INCLUDES -c "$source_path" -o "$object_file"
        
        if [ $? -ne 0 ]; then
            print_error "编译失败: $source"
            exit 1
        fi
        
        OBJECT_FILES+=("$object_file")
        print_info "✓ $source -> $object_file"
    done
    
    # 创建静态库
    print_info "创建静态库 $CORE_LIB_NAME..."
    $AR rcs "$CORE_LIB_NAME" "${OBJECT_FILES[@]}"
    
    if [ $? -ne 0 ]; then
        print_error "创建静态库失败"
        exit 1
    fi
    
    # 优化库文件
    print_info "优化库文件..."
    $STRIP --strip-unneeded "$CORE_LIB_NAME"
    
    print_success "核心库编译完成"
}

generate_version_info() {
    print_step "生成版本信息..."
    
    local version_file="$BUILD_DIR/VERSION"
    local build_date=$(date '+%Y-%m-%d %H:%M:%S')
    local build_hash=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    cat > "$version_file" << EOF
BL0906 Factory 预编译库版本信息
========================================
版本: $VERSION
构建日期: $build_date
Git提交: $build_hash
编译器: $CXX
目标架构: ESP32 (Xtensa)

核心功能:
- BL0906/BL0910 双芯片支持
- 运行时芯片型号切换
- 芯片参数管理 (从 bl0906_chip_params.h 集成)
- 校准系数计算 (从 bl0906_calibration.h 集成)
- 寄存器地址映射算法
- 数据转换算法
- 写保护解除算法
- 频率模式设置
- 电压采样模式支持

预编译保护:
- 寄存器地址映射表
- 校准系数计算公式
- 数据转换算法
- 写保护解除时序
- 芯片差异处理逻辑

构建配置:
- 优化级别: -Os (size优化)
- 调试信息: 已移除
- 符号表: 已剥离
- 异常处理: 已禁用
- RTTI: 已禁用
EOF
    
    print_success "版本信息生成完成"
}

create_release_package() {
    print_step "创建发布包..."
    
    # 复制核心库文件
    cp "$BUILD_DIR/$CORE_LIB_NAME" "$RELEASE_DIR/"
    
    # 复制头文件
    for header in "${HEADER_FILES[@]}"; do
        cp "$PROJECT_DIR/$header" "$RELEASE_DIR/"
    done
    
    # 复制版本信息
    cp "$BUILD_DIR/VERSION" "$RELEASE_DIR/"
    
    # 生成发布说明
    cat > "$RELEASE_DIR/README.md" << 'EOF'
# BL0906 Factory 预编译库

## 概述

这是BL0906 Factory组件的预编译核心库，包含了关键的算法实现和IP保护。

## 文件说明

- `libbl0906_core.a` - 预编译静态库
- `bl0906_core_api.h` - C接口头文件
- `VERSION` - 版本信息文件
- `README.md` - 说明文档

## 集成到ESPHome组件

1. 将 `libbl0906_core.a` 和 `bl0906_core_api.h` 复制到组件目录
2. 在薄包装层中包含 `bl0906_core_api.h`
3. 在组件配置中链接 `libbl0906_core.a`

## 核心功能

### 芯片参数管理
- 支持BL0906和BL0910两种芯片型号
- 运行时芯片型号识别和切换
- 寄存器地址映射算法
- 寄存器类型验证

### 校准系数计算
- 支持电压互感器采样模式
- 支持电阻分压采样模式
- 自动计算Ki、Kv、Kp、Ke系数
- 参考参数管理

### 数据转换
- 原始寄存器值转换为实际物理量
- 支持有符号/无符号数据类型
- 支持16位/24位寄存器

### 通信接口
- 抽象的通信回调接口
- 支持UART和SPI通信方式
- 写保护解除算法

## 使用示例

```cpp
#include "bl0906_core_api.h"

// 初始化回调函数
bl0906_comm_callbacks_t callbacks = {
    .read_register = my_read_register,
    .write_register = my_write_register,
    .send_raw_command = my_send_raw_command
};

// 初始化核心库
bl0906_result_t result = bl0906_core_init(
    BL0906_CHIP_BL0906,
    BL0906_COMM_UART,
    &callbacks
);

// 读取传感器数据
bl0906_sensor_data_t sensor_data;
result = bl0906_core_read_sensor_data(0, &sensor_data);

// 获取寄存器地址
uint8_t address;
result = bl0906_core_get_register_address(
    BL0906_CHIP_BL0906,
    BL0906_REG_I_RMS,
    0,
    &address
);

// 计算校准系数
bl0906_reference_params_t ref_params;
bl0906_calibration_coefficients_t coefficients;
result = bl0906_core_get_default_reference_params(
    BL0906_VOLTAGE_SAMPLING_TRANSFORMER,
    &ref_params
);
result = bl0906_core_calculate_calibration_coefficients(
    BL0906_VOLTAGE_SAMPLING_TRANSFORMER,
    &ref_params,
    &coefficients
);
```

## 版本历史

- v2.0.0: 集成芯片参数和校准系数功能
- v1.0.0: 初始版本，基础核心算法
EOF
    
    print_success "发布包创建完成"
}

show_library_info() {
    print_step "库文件信息..."
    
    local lib_path="$RELEASE_DIR/$CORE_LIB_NAME"
    local lib_size=$(du -h "$lib_path" | cut -f1)
    local symbol_count=$(nm "$lib_path" 2>/dev/null | wc -l || echo "N/A")
    
    echo
    echo -e "${YELLOW}库文件路径:${NC} $lib_path"
    echo -e "${YELLOW}库文件大小:${NC} $lib_size"
    echo -e "${YELLOW}符号数量:${NC} $symbol_count"
    echo
    
    # 显示导出的符号
    print_info "导出的公共符号:"
    if command -v nm &> /dev/null; then
        nm "$lib_path" 2>/dev/null | grep -E "^[0-9a-f]+ [TtWw] " | head -20 | while read line; do
            symbol=$(echo "$line" | awk '{print $3}')
            echo -e "  ${GREEN}•${NC} $symbol"
        done
        
        local total_symbols=$(nm "$lib_path" 2>/dev/null | grep -E "^[0-9a-f]+ [TtWw] " | wc -l)
        if [ "$total_symbols" -gt 20 ]; then
            echo -e "  ${YELLOW}... 还有 $((total_symbols - 20)) 个符号${NC}"
        fi
    else
        echo -e "  ${YELLOW}nm 工具不可用，无法显示符号信息${NC}"
    fi
    echo
}

cleanup() {
    print_step "清理临时文件..."
    
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
    fi
    
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    print_success "清理完成"
}

main() {
    print_header
    
    # 检查命令行参数
    SKIP_CLEANUP=false
    VERBOSE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                echo "使用方法: $0 [选项]"
                echo "选项:"
                echo "  --skip-cleanup    跳过清理步骤"
                echo "  --verbose         详细输出"
                echo "  --help            显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行构建步骤
    check_dependencies
    prepare_directories
    validate_sources
    compile_core_library
    generate_version_info
    create_release_package
    show_library_info
    
    if [ "$SKIP_CLEANUP" = false ]; then
        cleanup
    fi
    
    echo
    print_success "预编译库构建完成!"
    echo -e "${GREEN}发布文件位于:${NC} $RELEASE_DIR"
    echo -e "${GREEN}核心库文件:${NC} $RELEASE_DIR/$CORE_LIB_NAME"
    echo
    print_info "下一步操作:"
    echo "1. 将发布文件集成到ESPHome组件"
    echo "2. 更新薄包装层以使用预编译库"
    echo "3. 测试组件功能"
    echo "4. 部署到生产环境"
    echo
}

# 捕获退出信号进行清理
trap cleanup EXIT

# 运行主函数
main "$@" 