# BL0906Factory 内存优化解决方案

## 问题分析

设备重启的根本原因是**堆内存耗尽**，具体表现为：

```
assert failed: block_locate_free heap_tlsf.c:447 (block_size(block) >= size)
```

**错误发生路径：**
1. BL0906Factory::setup()
2. BL0906Factory::refresh_all_calib_numbers()  
3. BL0906Number::update_from_register()
4. Number::publish_state(float)
5. 日志字符串格式化时内存分配失败

**主要原因：**
- 配置中创建了58个校准Number组件（BL0906: 16个 + BL0910: 42个）
- 初始化时所有组件同时调用`publish_state()`
- ESP32堆内存碎片化，无法分配连续内存块

## 已实施的优化措施

### 1. 分批处理校准组件
- 每批最多处理5个组件
- 批次间延时100ms让系统回收内存
- 组件间延时10ms避免短时间大量内存分配

### 2. 降低日志级别
- 读取寄存器操作从INFO降至VERBOSE级别
- 减少字符串格式化的内存消耗

### 3. 内存监控和保护
- 初始化前检查可用堆内存
- 如果内存不足20KB则跳过校准组件刷新
- 添加内存使用情况日志

### 4. 优化类型转换
- 直接使用`static_cast<float>()`避免隐式转换
- 简化日志输出格式

## 立即解决方案

### 方案1：使用简化配置
使用提供的`simplified_test_config.yaml`：
- 禁用校准模式（calibration_mode: false）
- 只配置3个通道
- 延长更新间隔至30秒
- 降低日志级别

### 方案2：修改现有配置
在您的`16-ch-monitor-test.yaml`中添加：

```yaml
logger:
  level: INFO
  logs:
    bl0906_number: WARN
    sensor: WARN

# 在bl0906_factory配置中设置
bl0906_factory:
  - id: sensor_bl0906
    # ... 其他配置 ...
    calibration_mode: false  # 临时禁用校准
  - id: sensor_bl0910  
    # ... 其他配置 ...
    calibration_mode: false  # 临时禁用校准
```

### 方案3：分阶段初始化
如果需要保留所有校准组件，可以：
1. 先只启用一个芯片测试
2. 确认稳定后再启用第二个芯片
3. 逐步增加通道数量

## 测试步骤

1. **编译并上传简化配置**
   ```bash
   esphome compile simplified_test_config.yaml
   esphome upload simplified_test_config.yaml
   ```

2. **观察日志输出**
   - 查看"刷新校准组件前可用堆内存"
   - 确认没有内存不足警告
   - 验证设备正常运行

3. **逐步恢复功能**
   - 稳定运行后，逐步启用更多传感器
   - 监控内存使用情况
   - 如需要校准功能，可后期单独启用

## 长期解决方案

1. **优化内存分配策略**
   - 使用对象池管理Number组件
   - 实现延迟初始化机制

2. **减少组件数量**
   - 按需配置校准组件
   - 使用动态创建而非静态定义

3. **内存碎片管理**
   - 定期进行内存整理
   - 使用更大的内存块分配

## 监控命令

在设备正常运行后，可以通过以下方式监控内存：

```yaml
sensor:
  - platform: debug
    free:
      name: "Heap Free"
    block:
      name: "Heap Max Block"
debug:
  update_interval: 10s
```

## 注意事项

- **临时禁用校准功能不影响基本传感器读取**
- **后续可以通过OTA逐步恢复完整功能**
- **建议在稳定后逐步测试添加更多组件**
- **如果问题持续，可能需要考虑使用更大内存的ESP32变种** 