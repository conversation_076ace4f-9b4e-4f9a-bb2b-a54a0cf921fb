@echo off
setlocal EnableDelayedExpansion

REM BL0906 Factory Precompiled Library Build Script (Robust Version)
REM This script handles special characters and spaces in PATH

REM Project Configuration
set "PROJECT_NAME=bl0906_factory"
set "CORE_LIB_NAME=libbl0906_core.a"
set "VERSION=2.0.0"

REM Directory Configuration (using quotes to handle spaces)
set "SCRIPT_DIR=%~dp0"
set "PROJECT_DIR=%SCRIPT_DIR%"
set "BUILD_DIR=%PROJECT_DIR%build"
set "RELEASE_DIR=%PROJECT_DIR%release"
set "TEMP_DIR=%PROJECT_DIR%temp"

REM Source Files Configuration (using full paths with quotes)
set "CORE_SOURCES=%PROJECT_DIR%bl0906_core_impl.cpp"
set "HEADER_FILES=%PROJECT_DIR%bl0906_core_api.h"

REM Compiler Configuration (requires ESP-IDF environment)
set "CC=xtensa-esp32-elf-gcc.exe"
set "CXX=xtensa-esp32-elf-g++.exe"
set "AR=xtensa-esp32-elf-ar.exe"
set "STRIP=xtensa-esp32-elf-strip.exe"

REM Compilation Options (using quotes)
set "CFLAGS=-Os -g0 -DNDEBUG -ffunction-sections -fdata-sections -fstrict-aliasing -mlongcalls"
set "CXXFLAGS=%CFLAGS% -std=c++17 -fno-rtti -fno-exceptions"
set "INCLUDES=-I. -I\"%PROJECT_DIR%\""

echo ================================================
echo   BL0906 Factory Precompiled Library Builder v%VERSION%
echo   Robust Version (Handles PATH Issues)
echo ================================================
echo.

:check_dependencies
echo [STEP] Checking build dependencies...

REM Check ESP-IDF environment
if "%IDF_PATH%"=="" (
    echo [ERROR] IDF_PATH environment variable not set
    echo [INFO] Please run ESP-IDF export.bat script first
    echo.
    echo [SOLUTION] Common ESP-IDF installation paths:
    echo   C:\Espressif\frameworks\esp-idf-v4.4\export.bat
    echo   C:\Espressif\frameworks\esp-idf-v5.0\export.bat
    echo   %%USERPROFILE%%\esp\esp-idf\export.bat
    echo.
    pause
    exit /b 1
)

echo [INFO] ESP-IDF found at: %IDF_PATH%

REM Check compilers with better error handling
echo [INFO] Checking for ESP32 toolchain...

REM Use full path checking to avoid PATH parsing issues
set "COMPILER_FOUND=0"

REM Try to find compiler in common ESP-IDF locations
for %%P in (
    "%IDF_PATH%\tools\xtensa-esp32-elf\*\xtensa-esp32-elf\bin"
    "C:\Espressif\tools\xtensa-esp32-elf\*\xtensa-esp32-elf\bin"
    "%USERPROFILE%\.espressif\tools\xtensa-esp32-elf\*\xtensa-esp32-elf\bin"
) do (
    if exist "%%P\xtensa-esp32-elf-g++.exe" (
        set "COMPILER_PATH=%%P"
        set "COMPILER_FOUND=1"
        echo [INFO] Found ESP32 toolchain at: %%P
        goto :compiler_found
    )
)

:compiler_found
if "%COMPILER_FOUND%"=="0" (
    echo [ERROR] ESP32 toolchain not found in standard locations
    echo [INFO] Please ensure ESP-IDF is properly installed and configured
    echo.
    echo [DEBUG] Trying direct PATH search...
    where xtensa-esp32-elf-g++.exe 2>nul
    if errorlevel 1 (
        echo [ERROR] xtensa-esp32-elf-g++.exe not found in PATH
        echo [INFO] This usually means ESP-IDF export.bat was not run
    ) else (
        echo [INFO] Found compiler in PATH, continuing...
        set "COMPILER_FOUND=1"
    )
)

if "%COMPILER_FOUND%"=="0" (
    echo.
    echo [SOLUTION] To fix this issue:
    echo 1. Open a new Command Prompt
    echo 2. Run: [ESP-IDF-PATH]\export.bat
    echo 3. Run this script in the same Command Prompt
    echo.
    pause
    exit /b 1
)

echo [SUCCESS] ESP32 toolchain verification completed

:prepare_directories
echo [STEP] Preparing build directories...

REM Clean old build directories (with proper quoting)
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%" 2>nul
)

if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%" 2>nul
)

REM Create directories (with error checking)
mkdir "%BUILD_DIR%" 2>nul
if not exist "%BUILD_DIR%" (
    echo [ERROR] Failed to create build directory: %BUILD_DIR%
    pause
    exit /b 1
)

mkdir "%RELEASE_DIR%" 2>nul
if not exist "%RELEASE_DIR%" (
    echo [ERROR] Failed to create release directory: %RELEASE_DIR%
    pause
    exit /b 1
)

mkdir "%TEMP_DIR%" 2>nul

echo [SUCCESS] Directory preparation completed

:validate_sources
echo [STEP] Validating source files...

if not exist "%CORE_SOURCES%" (
    echo [ERROR] Source file not found: %CORE_SOURCES%
    echo [INFO] Current directory: %CD%
    echo [INFO] Project directory: %PROJECT_DIR%
    echo [INFO] Please ensure you run this script in the correct directory
    pause
    exit /b 1
)
echo [INFO] Found: bl0906_core_impl.cpp

if not exist "%HEADER_FILES%" (
    echo [ERROR] Header file not found: %HEADER_FILES%
    echo [INFO] Please ensure all required files are present
    pause
    exit /b 1
)
echo [INFO] Found: bl0906_core_api.h

echo [SUCCESS] Source file validation completed

:compile_core_library
echo [STEP] Compiling core library...

REM Change to build directory (with proper quoting)
pushd "%BUILD_DIR%"

REM Compile source files
set "object_file=bl0906_core_impl.o"

echo [INFO] Compiling bl0906_core_impl.cpp...
echo [INFO] Source: %CORE_SOURCES%
echo [INFO] Output: %object_file%
echo.

REM Use a more robust compilation approach
echo [DEBUG] Compilation details:
echo   Working directory: %CD%
echo   Compiler: %CXX%
echo   Source file: "%CORE_SOURCES%"
echo   Object file: "%object_file%"
echo.

REM Compile with explicit error handling
"%CXX%" %CXXFLAGS% %INCLUDES% -c "%CORE_SOURCES%" -o "%object_file%" 2>&1

if errorlevel 1 (
    echo [ERROR] Compilation failed
    echo [INFO] This could be due to:
    echo   1. Source code syntax errors
    echo   2. Missing header files
    echo   3. Compiler configuration issues
    echo   4. PATH environment problems
    echo.
    echo [DEBUG] Try running the compiler directly:
    echo "%CXX%" --version
    echo.
    popd
    pause
    exit /b 1
)

REM Verify object file was created
if not exist "%object_file%" (
    echo [ERROR] Object file was not generated: %object_file%
    echo [INFO] Compilation may have failed silently
    popd
    pause
    exit /b 1
)

echo [INFO] Successfully compiled to: %object_file%

REM Create static library
echo [INFO] Creating static library: %CORE_LIB_NAME%
"%AR%" rcs "%CORE_LIB_NAME%" "%object_file%" 2>&1

if errorlevel 1 (
    echo [ERROR] Static library creation failed
    popd
    pause
    exit /b 1
)

REM Verify library was created
if not exist "%CORE_LIB_NAME%" (
    echo [ERROR] Static library was not generated: %CORE_LIB_NAME%
    popd
    pause
    exit /b 1
)

REM Optimize library (optional, don't fail if this doesn't work)
echo [INFO] Optimizing library file...
"%STRIP%" --strip-unneeded "%CORE_LIB_NAME%" 2>nul
if errorlevel 1 (
    echo [WARNING] Library optimization failed, but this is not critical
)

echo [SUCCESS] Core library compilation completed

REM Return to original directory
popd

:generate_version_info
echo [STEP] Generating version information...

set "version_file=%BUILD_DIR%\VERSION"

REM Get current date and time (robust method)
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "build_date=%dt:~0,4%-%dt:~4,2%-%dt:~6,2%"
set "build_time=%dt:~8,2%:%dt:~10,2%:%dt:~12,2%"

REM Try to get Git commit hash (with error handling)
set "build_hash=unknown"
git rev-parse --short HEAD >"%TEMP_DIR%\hash.tmp" 2>nul
if not errorlevel 1 (
    set /p build_hash=<"%TEMP_DIR%\hash.tmp"
)
if exist "%TEMP_DIR%\hash.tmp" del "%TEMP_DIR%\hash.tmp"

REM Generate version file
(
echo BL0906 Factory Precompiled Library Version Information
echo ========================================================
echo Version: %VERSION%
echo Build Date: %build_date% %build_time%
echo Git Commit: %build_hash%
echo Compiler: %CXX%
echo Target Architecture: ESP32 (Xtensa^)
echo Build Platform: Windows (Robust Version^)
echo.
echo Core Features:
echo - BL0906/BL0910 dual chip support
echo - Runtime chip model switching
echo - Chip parameter management
echo - Calibration coefficient calculation
echo - Register address mapping algorithm
echo - Data conversion algorithm
echo - Write protection unlock algorithm
echo - Frequency mode setting
echo - Voltage sampling mode support
) > "%version_file%"

echo [SUCCESS] Version information generated

:create_release_package
echo [STEP] Creating release package...

REM Copy files to release directory
copy "%BUILD_DIR%\%CORE_LIB_NAME%" "%RELEASE_DIR%\" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Failed to copy library file to release directory
    pause
    exit /b 1
)

copy "%HEADER_FILES%" "%RELEASE_DIR%\" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Failed to copy header file to release directory
    pause
    exit /b 1
)

copy "%BUILD_DIR%\VERSION" "%RELEASE_DIR%\" >nul 2>&1

REM Generate README
(
echo # BL0906 Factory Precompiled Library (Windows Robust Build^)
echo.
echo ## Overview
echo.
echo This is the precompiled core library for BL0906 Factory component.
echo Built on Windows using ESP-IDF toolchain with robust error handling.
echo.
echo ## Files
echo.
echo - libbl0906_core.a - Precompiled static library
echo - bl0906_core_api.h - C interface header file
echo - VERSION - Version information
echo - README.md - This documentation
echo.
echo ## Build Information
echo.
echo - Version: %VERSION%
echo - Platform: Windows
echo - Build Script: Robust version with PATH issue handling
echo - Compiler: ESP32 Xtensa GCC
) > "%RELEASE_DIR%\README.md"

echo [SUCCESS] Release package created

:show_results
echo [STEP] Build results...

set "lib_path=%RELEASE_DIR%\%CORE_LIB_NAME%"

if exist "%lib_path%" (
    for %%A in ("%lib_path%") do set "lib_size=%%~zA"
    echo.
    echo [SUCCESS] Build completed successfully!
    echo   Library: %lib_path%
    echo   Size: !lib_size! bytes
) else (
    echo [ERROR] Library file not found in release directory
    exit /b 1
)

echo.
echo Release package contents:
dir "%RELEASE_DIR%" /b 2>nul

:cleanup
echo.
echo [STEP] Cleaning up...

if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%" 2>nul
)

if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%" 2>nul
)

echo [SUCCESS] Cleanup completed

:finish
echo.
echo ================================================
echo [SUCCESS] BL0906 Factory precompiled library build completed!
echo ================================================
echo.
echo Release files: %RELEASE_DIR%
echo.
echo Next steps:
echo 1. Integrate files into ESPHome component
echo 2. Update wrapper layer to use precompiled library
echo 3. Test component functionality
echo.
pause 