@echo off
chcp 65001 >nul
setlocal EnableDelayedExpansion

REM BL0906 Factory 预编译库构建脚本 (Windows版本 - 路径修复版)
REM 此脚本将核心算法编译为预编译库，保护关键IP

REM 项目配置
set PROJECT_NAME=bl0906_factory
set CORE_LIB_NAME=libbl0906_core.a
set VERSION=2.0.0

REM 目录配置
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%
set BUILD_DIR=%PROJECT_DIR%build
set RELEASE_DIR=%PROJECT_DIR%release
set TEMP_DIR=%PROJECT_DIR%temp

REM 源文件配置 (使用完整路径)
set CORE_SOURCES=%PROJECT_DIR%bl0906_core_impl.cpp
set HEADER_FILES=%PROJECT_DIR%bl0906_core_api.h

REM 编译器配置 (需要ESP-IDF环境)
set CC=xtensa-esp32-elf-gcc.exe
set CXX=xtensa-esp32-elf-g++.exe
set AR=xtensa-esp32-elf-ar.exe
set STRIP=xtensa-esp32-elf-strip.exe

REM 编译选项
set CFLAGS=-Os -g0 -DNDEBUG -ffunction-sections -fdata-sections -fstrict-aliasing -mlongcalls
set CXXFLAGS=%CFLAGS% -std=c++17 -fno-rtti -fno-exceptions
set INCLUDES=-I. -I"%PROJECT_DIR%"

echo ================================================
echo   BL0906 Factory 预编译库构建工具 v%VERSION%
echo   Windows版本 (路径修复版)
echo ================================================
echo.

:check_dependencies
echo [步骤] 检查构建依赖...

REM 检查ESP-IDF环境
if "%IDF_PATH%"=="" (
    echo [错误] 未设置IDF_PATH环境变量
    echo [信息] 请先运行ESP-IDF的export.bat脚本
    echo [信息] 例如: %%USERPROFILE%%\esp\esp-idf\export.bat
    echo.
    echo [解决方案] 请按以下步骤操作:
    echo 1. 打开命令提示符
    echo 2. 运行: %%USERPROFILE%%\esp\esp-idf\export.bat
    echo 3. 在同一个命令提示符中重新运行此脚本
    echo.
    pause
    exit /b 1
)

REM 检查编译器
where %CC% >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到 %CC% 编译器
    echo [信息] 请确保ESP-IDF环境已正确配置
    echo [信息] 当前PATH中未找到ESP32工具链
    echo.
    pause
    exit /b 1
)

where %CXX% >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到 %CXX% 编译器
    echo [信息] 请确保ESP-IDF环境已正确配置
    pause
    exit /b 1
)

where %AR% >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到 %AR% 归档工具
    echo [信息] 请确保ESP-IDF环境已正确配置
    pause
    exit /b 1
)

echo [成功] 所有依赖检查通过

:prepare_directories
echo [步骤] 准备构建目录...

REM 清理旧的构建目录
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
)

if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%"
)

REM 创建目录
mkdir "%BUILD_DIR%" 2>nul
mkdir "%RELEASE_DIR%" 2>nul
mkdir "%TEMP_DIR%" 2>nul

echo [成功] 目录准备完成

:validate_sources
echo [步骤] 验证源文件...

if not exist "%CORE_SOURCES%" (
    echo [错误] 源文件不存在: %CORE_SOURCES%
    echo [信息] 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)
echo [信息] ✓ bl0906_core_impl.cpp

if not exist "%HEADER_FILES%" (
    echo [错误] 头文件不存在: %HEADER_FILES%
    echo [信息] 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)
echo [信息] ✓ bl0906_core_api.h

echo [成功] 源文件验证完成

:compile_core_library
echo [步骤] 编译核心库...

cd /d "%BUILD_DIR%"

REM 编译源文件
set object_file=bl0906_core_impl.o

echo [信息] 编译 bl0906_core_impl.cpp...
echo [信息] 源文件路径: %CORE_SOURCES%
echo [信息] 输出文件: %object_file%
echo.

REM 构建编译命令 (分行显示便于调试)
echo [调试] 编译命令详情:
echo   编译器: %CXX%
echo   源文件: "%CORE_SOURCES%"
echo   输出: "%object_file%"
echo   选项: %CXXFLAGS%
echo   包含: %INCLUDES%
echo.

REM 编译C++源文件
"%CXX%" %CXXFLAGS% %INCLUDES% -c "%CORE_SOURCES%" -o "%object_file%"

if errorlevel 1 (
    echo [错误] 编译失败: bl0906_core_impl.cpp
    echo [信息] 请检查源代码是否有语法错误
    echo [调试] 尝试手动运行编译命令进行调试
    pause
    exit /b 1
)

echo [信息] ✓ bl0906_core_impl.cpp -^> %object_file%

REM 验证目标文件是否生成
if not exist "%object_file%" (
    echo [错误] 目标文件未生成: %object_file%
    pause
    exit /b 1
)

REM 创建静态库
echo [信息] 创建静态库 %CORE_LIB_NAME%...
"%AR%" rcs "%CORE_LIB_NAME%" "%object_file%"

if errorlevel 1 (
    echo [错误] 创建静态库失败
    pause
    exit /b 1
)

REM 验证静态库是否生成
if not exist "%CORE_LIB_NAME%" (
    echo [错误] 静态库文件未生成: %CORE_LIB_NAME%
    pause
    exit /b 1
)

REM 优化库文件
echo [信息] 优化库文件...
"%STRIP%" --strip-unneeded "%CORE_LIB_NAME%" 2>nul

if errorlevel 1 (
    echo [警告] 优化库文件失败，但不影响功能
)

echo [成功] 核心库编译完成

:generate_version_info
echo [步骤] 生成版本信息...

set version_file=%BUILD_DIR%\VERSION

REM 获取当前时间
for /f "tokens=1-4 delims=/ " %%a in ('date /t') do (
    set build_date=%%a/%%b/%%c
)
for /f "tokens=1-2 delims=: " %%a in ('time /t') do (
    set build_time=%%a:%%b
)

REM 尝试获取Git提交哈希
git rev-parse --short HEAD >temp_hash.txt 2>nul
if errorlevel 1 (
    set build_hash=unknown
) else (
    set /p build_hash=<temp_hash.txt
)
if exist temp_hash.txt del temp_hash.txt

echo BL0906 Factory 预编译库版本信息 > "%version_file%"
echo ======================================== >> "%version_file%"
echo 版本: %VERSION% >> "%version_file%"
echo 构建日期: %build_date% %build_time% >> "%version_file%"
echo Git提交: %build_hash% >> "%version_file%"
echo 编译器: %CXX% >> "%version_file%"
echo 目标架构: ESP32 (Xtensa) >> "%version_file%"
echo 构建平台: Windows (路径修复版) >> "%version_file%"
echo. >> "%version_file%"
echo 核心功能: >> "%version_file%"
echo - BL0906/BL0910 双芯片支持 >> "%version_file%"
echo - 运行时芯片型号切换 >> "%version_file%"
echo - 芯片参数管理 (从 bl0906_chip_params.h 集成) >> "%version_file%"
echo - 校准系数计算 (从 bl0906_calibration.h 集成) >> "%version_file%"
echo - 寄存器地址映射算法 >> "%version_file%"
echo - 数据转换算法 >> "%version_file%"
echo - 写保护解除算法 >> "%version_file%"
echo - 频率模式设置 >> "%version_file%"
echo - 电压采样模式支持 >> "%version_file%"

echo [成功] 版本信息生成完成

:create_release_package
echo [步骤] 创建发布包...

REM 复制核心库文件
copy "%BUILD_DIR%\%CORE_LIB_NAME%" "%RELEASE_DIR%\" >nul

REM 复制头文件
copy "%HEADER_FILES%" "%RELEASE_DIR%\" >nul

REM 复制版本信息
copy "%BUILD_DIR%\VERSION" "%RELEASE_DIR%\" >nul

REM 生成发布说明
echo # BL0906 Factory Precompiled Library (Windows Fixed Build) > "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo ## Overview >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo This is the precompiled core library for BL0906 Factory component. >> "%RELEASE_DIR%\README.md"
echo Built on Windows using ESP-IDF toolchain with path fixes. >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo ## Build Information >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo - Version: %VERSION% >> "%RELEASE_DIR%\README.md"
echo - Platform: Windows >> "%RELEASE_DIR%\README.md"
echo - Compiler: ESP32 Xtensa GCC >> "%RELEASE_DIR%\README.md"
echo - Encoding: UTF-8 >> "%RELEASE_DIR%\README.md"
echo - Path Handling: Fixed >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo ## Files >> "%RELEASE_DIR%\README.md"
echo. >> "%RELEASE_DIR%\README.md"
echo - libbl0906_core.a - Precompiled static library >> "%RELEASE_DIR%\README.md"
echo - bl0906_core_api.h - C interface header file >> "%RELEASE_DIR%\README.md"
echo - VERSION - Version information >> "%RELEASE_DIR%\README.md"
echo - README.md - Documentation >> "%RELEASE_DIR%\README.md"

echo [成功] 发布包创建完成

:show_library_info
echo [步骤] 库文件信息...

set lib_path=%RELEASE_DIR%\%CORE_LIB_NAME%

REM 获取文件大小
for %%A in ("%lib_path%") do set lib_size=%%~zA

echo.
echo 库文件路径: %lib_path%
echo 库文件大小: %lib_size% 字节

REM 显示文件列表
echo.
echo [信息] 发布包内容:
dir "%RELEASE_DIR%" /b
echo.

:cleanup
echo [步骤] 清理临时文件...

if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%"
)

if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%"
)

echo [成功] 清理完成

:finish
echo.
echo ================================================
echo [成功] 预编译库构建完成!
echo ================================================
echo.
echo 发布文件位于: %RELEASE_DIR%
echo 核心库文件: %RELEASE_DIR%\%CORE_LIB_NAME%
echo.
echo 文件验证:
if exist "%RELEASE_DIR%\%CORE_LIB_NAME%" (
    echo ✓ 静态库文件已生成
) else (
    echo ✗ 静态库文件生成失败
)

if exist "%RELEASE_DIR%\bl0906_core_api.h" (
    echo ✓ 头文件已复制
) else (
    echo ✗ 头文件复制失败
)

if exist "%RELEASE_DIR%\VERSION" (
    echo ✓ 版本信息已生成
) else (
    echo ✗ 版本信息生成失败
)

echo.
echo 下一步操作:
echo 1. 将发布文件集成到ESPHome组件
echo 2. 更新薄包装层以使用预编译库
echo 3. 测试组件功能
echo 4. 部署到生产环境
echo.
pause 