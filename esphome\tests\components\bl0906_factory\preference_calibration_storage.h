#pragma once
#include "calibration_storage_base.h"
#include "esphome/core/preferences.h"
#include <string>
#include <vector>

namespace esphome {
namespace bl0906_factory {

class PreferenceCalibrationStorage : public CalibrationStorageBase {
public:
    PreferenceCalibrationStorage();
    
    bool init() override;
    bool verify() override;
    bool erase() override;
    
    std::vector<uint32_t> get_instance_list() override;
    size_t get_max_instances() override { return 16; } // NVS支持更多实例
    std::string get_storage_type() const override { return "preference"; }

protected:
    // 实现基类的纯虚函数
    StorageResult read_raw_data(uint32_t instance_id, uint8_t* buffer, size_t& buffer_size) override;
    StorageResult write_raw_data(uint32_t instance_id, const uint8_t* buffer, size_t buffer_size) override;
    StorageResult delete_raw_data(uint32_t instance_id) override;
    
    // 重写配置方法
    const char* get_log_tag() const override { return TAG; }

public:
    // 诊断和清除方法
    void diagnose_nvs_storage(uint32_t instance_id);
    bool completely_erase_all_data();

private:
    std::string get_preference_key(uint32_t instance_id);
    bool save_instance_list();
    bool load_instance_list();
    void scan_existing_instances();
    bool verify_instance_exists(uint32_t instance_id);


    std::vector<uint32_t> instance_list_;
    static const char *const TAG;
};

}  // namespace bl0906_factory
}  // namespace esphome 