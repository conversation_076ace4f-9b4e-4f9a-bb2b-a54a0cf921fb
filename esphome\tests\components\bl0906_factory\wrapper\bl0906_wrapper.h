#pragma once

#include "esphome/core/component.h"
#include "esphome/core/hal.h"
#include "esphome/core/log.h"
#include "esphome/components/sensor/sensor.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/spi/spi.h"

// 预编译库接口
#include "bl0906_core_api.h"

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "bl0906_factory";

// 芯片型号枚举（映射到预编译库）
enum ChipModel {
  CHIP_BL0906 = 0,
  CHIP_BL0910 = 1
};

// 电压采样模式枚举（映射到预编译库）
enum VoltageSamplingMode {
  VOLTAGE_SAMPLING_TRANSFORMER = 0,
  VOLTAGE_SAMPLING_RESISTOR_DIVIDER = 1
};

// 通信方式枚举
enum CommunicationType {
  COMMUNICATION_UART = 0,
  COMMUNICATION_SPI = 1
};

// 校准参数结构（映射到预编译库）
struct CalibrationParams {
  float vref = 1.097f;      // 内部参考电压
  int gain_v = 1;           // 电压通道增益
  int gain_i = 1;           // 电流通道增益
  float rl = 5.1f;          // 负载电阻
  float rt = 2000.0f;       // 互感器变比
  float rf = 100000.0f;     // 分压电阻
  float r46 = 100.0f;       // 电压采样电阻
  float rv = 1.0f;          // 分压下拉电阻
};

// 校准系数结构（映射到预编译库）
struct CalibrationCoefficients {
  float ki = 0.0f;      // 电流系数
  float kv = 0.0f;      // 电压系数
  float kp = 0.0f;      // 功率系数
  float ke = 0.0f;      // 电量系数
  float kp_sum = 0.0f;  // 总功率系数
  float ke_sum = 0.0f;  // 总电量系数
  float fref = 0.0f;    // 频率转换系数
  float tref = 0.0f;    // 温度转换系数
};

// 前向声明
class BL0906FactoryComponent;

/**
 * @brief 薄包装层主组件类
 * 
 * 这个类作为ESPHome组件和预编译库之间的适配层，提供：
 * - 预编译库的初始化和管理
 * - 芯片参数的运行时配置
 * - 校准系数的计算和应用
 * - 传感器数据的读取和发布
 */
class BL0906FactoryComponent : public Component {
 public:
  // 构造函数
  BL0906FactoryComponent() = default;

  // ESPHome组件生命周期
  void setup() override;
  void loop() override;
  void dump_config() override;
  float get_setup_priority() const override { return setup_priority::DATA; }

  // 芯片配置
  void set_chip_model(ChipModel model) { chip_model_ = model; }
  ChipModel get_chip_model() const { return chip_model_; }
  
  // 通信配置
  void set_communication_type(CommunicationType type) { comm_type_ = type; }
  void set_uart_parent(uart::UARTComponent *parent) { uart_parent_ = parent; }
  void set_spi_parent(spi::SPIComponent *parent) { spi_parent_ = parent; }
  void set_cs_pin(GPIOPin *cs_pin) { cs_pin_ = cs_pin; }

  // 校准配置
  void set_voltage_sampling_mode(VoltageSamplingMode mode) { voltage_sampling_mode_ = mode; }
  void set_calibration_params(const CalibrationParams &params) { calib_params_ = params; }
  
  // 传感器注册
  void register_sensor(sensor::Sensor *sensor, const std::string &type, int channel = -1);
  
  // 校准功能
  bool calculate_calibration_coefficients();
  bool apply_calibration_to_chip();
  
  // 数据读取
  bool read_sensor_data(int channel, float *voltage, float *current, float *power, float *energy);
  bool read_global_data(float *voltage, float *frequency, float *temperature);
  
  // 工具函数
  bool get_register_address(const std::string &reg_type, int channel, uint8_t *address);
  bool validate_register_address(uint8_t address);
  
  // 错误处理
  std::string get_last_error() const { return last_error_; }

 protected:
  // 预编译库回调函数（静态）
  static bl0906_result_t read_register_callback(uint8_t address, int32_t *value);
  static bl0906_result_t write_register_callback(uint8_t address, int16_t value);
  static bl0906_result_t send_raw_command_callback(const uint8_t *data, size_t length);
  
  // 实际的通信实现
  bl0906_result_t read_register_impl(uint8_t address, int32_t *value);
  bl0906_result_t write_register_impl(uint8_t address, int16_t value);
  bl0906_result_t send_raw_command_impl(const uint8_t *data, size_t length);

 private:
  // 芯片配置
  ChipModel chip_model_ = CHIP_BL0906;
  CommunicationType comm_type_ = COMMUNICATION_UART;
  VoltageSamplingMode voltage_sampling_mode_ = VOLTAGE_SAMPLING_TRANSFORMER;
  
  // 通信接口
  uart::UARTComponent *uart_parent_ = nullptr;
  spi::SPIComponent *spi_parent_ = nullptr;
  GPIOPin *cs_pin_ = nullptr;
  
  // 校准参数
  CalibrationParams calib_params_;
  CalibrationCoefficients calib_coeffs_;
  
  // 传感器映射
  std::map<std::string, std::vector<sensor::Sensor *>> sensors_;
  
  // 状态管理
  bool initialized_ = false;
  std::string last_error_;
  
  // 静态实例指针（用于回调）
  static BL0906FactoryComponent *instance_;
  
  // 内部辅助函数
  bool initialize_precompiled_library();
  void update_sensors();
  void set_error(const std::string &error);
  
  // 寄存器类型映射
  bl0906_register_type_t map_register_type(const std::string &type);
  
  // 数据转换
  bl0906_chip_model_t map_chip_model(ChipModel model);
  bl0906_voltage_sampling_mode_t map_voltage_sampling_mode(VoltageSamplingMode mode);
  bl0906_comm_type_t map_communication_type(CommunicationType type);
};

}  // namespace bl0906_factory
}  // namespace esphome 