#pragma once

#include "communication_adapter_interface.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include "esphome/core/hal.h"
#include <functional>

namespace esphome {
namespace bl0906_factory {

/**
 * 通信适配器基类
 * 
 * 提供通用的错误处理、统计管理和重试机制功能
 * 消除UART和SPI适配器中的重复代码
 */
class CommunicationAdapterBase : public CommunicationAdapterInterface {
public:
  CommunicationAdapterBase() = default;
  ~CommunicationAdapterBase() override = default;

  // ========== 通用接口实现 ==========
  
  std::string get_last_error() const override;
  void reset_error_state() override;
  CommunicationError get_last_error_code() const override;
  
  size_t get_success_count() const override;
  size_t get_error_count() const override;
  CommunicationStats get_statistics() const override;
  void reset_statistics() override;

protected:
  // ========== 保护的状态变量 ==========
  
  bool initialized_ = false;
  CommunicationStats stats_;
  CommunicationError last_error_ = CommunicationError::SUCCESS;
  std::string last_error_message_;
  
  // ========== 配置参数 ==========
  
  static constexpr uint32_t DEFAULT_RETRY_COUNT = 3;
  static constexpr uint32_t RETRY_DELAY_MS = 10;
  
  // ========== 保护的通用方法 ==========
  
  /**
   * 设置错误状态
   * @param error 错误码
   * @param message 错误消息
   */
  void set_error(CommunicationError error, const std::string& message);
  
  /**
   * 更新统计信息
   * @param success 操作是否成功
   * @param error 错误码（如果失败）
   */
  void update_statistics(bool success, CommunicationError error = CommunicationError::SUCCESS);
  
  /**
   * 执行带重试的操作
   * @param operation 要执行的操作函数
   * @param max_retries 最大重试次数
   * @param retry_delay_ms 重试间延时（毫秒）
   * @param should_flush_on_retry 重试前是否清空缓冲区
   * @return 操作结果
   */
  template<typename T>
  T execute_with_retry(std::function<T()> operation, 
                       int max_retries = DEFAULT_RETRY_COUNT,
                       uint32_t retry_delay_ms = RETRY_DELAY_MS,
                       bool should_flush_on_retry = true);
  
  /**
   * 检查适配器是否已初始化
   * @return true 已初始化，false 未初始化
   */
  bool check_initialized() const;
  
  /**
   * 验证寄存器读取/写入的结果是否匹配
   * @param expected_value 期望值
   * @param actual_value 实际值
   * @param register_address 寄存器地址（用于错误消息）
   * @return true 匹配，false 不匹配
   */
  bool verify_register_value(int16_t expected_value, int32_t actual_value, uint8_t register_address);
  
  /**
   * 记录操作开始的日志
   * @param operation_name 操作名称
   * @param register_address 寄存器地址
   * @param value 操作的值（可选）
   */
  void log_operation_start(const std::string& operation_name, uint8_t register_address, int32_t value = 0);
  
  /**
   * 记录操作结果的日志
   * @param operation_name 操作名称
   * @param register_address 寄存器地址
   * @param success 是否成功
   * @param value 结果值（可选）
   */
  void log_operation_result(const std::string& operation_name, uint8_t register_address, bool success, int32_t value = 0);

  // ========== 纯虚函数 - 子类必须实现 ==========
  
  /**
   * 刷新通信缓冲区（协议特定实现）
   */
  virtual void flush_buffer() = 0;
  
  /**
   * 获取适配器类型名称
   */
  virtual std::string get_adapter_type() const = 0;

protected:
  // ========== 内部日志标签 ==========
  static constexpr const char* TAG = "comm_adapter_base";
};

// ========== 模板方法实现 ==========

template<typename T>
T CommunicationAdapterBase::execute_with_retry(std::function<T()> operation, 
                                                int max_retries,
                                                uint32_t retry_delay_ms,
                                                bool should_flush_on_retry) {
  T result = T();
  
  for (int attempt = 0; attempt <= max_retries; attempt++) {
    // 重置错误状态
    reset_error_state();
    
    // 执行操作
    result = operation();
    
    // 检查操作是否成功
    bool operation_success = (last_error_ == CommunicationError::SUCCESS);
    
    if (operation_success) {
      ESP_LOGV(TAG, "%s适配器操作成功，第%d次尝试", 
               get_adapter_type().c_str(), attempt + 1);
      return result;
    }
    
    // 如果还有重试机会
    if (attempt < max_retries) {
      ESP_LOGW(TAG, "%s适配器操作失败，第%d次重试... (错误: %s)", 
               get_adapter_type().c_str(), attempt + 1, last_error_message_.c_str());
      
             // 重试前延时
       if (retry_delay_ms > 0) {
         esphome::delay(retry_delay_ms);
       }
      
      // 重试前清空缓冲区（如果需要）
      if (should_flush_on_retry) {
        flush_buffer();
      }
    } else {
      ESP_LOGW(TAG, "%s适配器操作失败，已达到最大重试次数(%d) - 错误: %s", 
               get_adapter_type().c_str(), max_retries + 1, last_error_message_.c_str());
    }
  }
  
  return result;
}

} // namespace bl0906_factory
} // namespace esphome 