#pragma once

namespace esphome {
namespace bl0906_factory {

// 通用参数定义
static constexpr float Vref = 1.097f;           // 内部参考电压 (V)
static constexpr int Gain_V = 1;                // 电压通道增益 (1, 2, 8, 16)
static constexpr int Gain_I = 1;                // 电流通道增益 (1, 2, 8, 16)
static constexpr float RL = 5.1f;               // 互感器副边负载电阻 (Ω)
static constexpr float Rt = 2000.0f;            // 互感器变比，如 2000:1

// 检测冲突定义
#if defined(BL0906_VOLTAGE_SAMPLING_TRANSFORMER) && defined(BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER)
#error "不能同时定义 BL0906_VOLTAGE_SAMPLING_TRANSFORMER 和 BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER"
#endif

// 设置默认值：如果没有明确定义，默认使用电压互感器方式
#if !defined(BL0906_VOLTAGE_SAMPLING_TRANSFORMER) && !defined(BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER)
#define BL0906_VOLTAGE_SAMPLING_TRANSFORMER
#endif

// 条件编译配置：BL0906_VOLTAGE_SAMPLING_TRANSFORMER 或 BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER
#ifdef BL0906_VOLTAGE_SAMPLING_TRANSFORMER
    // 电压互感器采样方式参数
    static constexpr float Rf = 100000.0f;      // 分压上拉电阻 (Ω)  
    static constexpr int R46 = 100;             // 电压采样电阻 (Ω)
    
    // 校准系数计算
    static constexpr float Ki = (12875 * Gain_I * (RL + RL) * 1000 / Rt) / Vref;
    static constexpr float Kv = (13162 * Gain_V * R46 * 1000) / (Vref * Rf);
    static constexpr float Kp = 2.3847e-7 * Ki * Kv;
    static constexpr float Ke = (3600000.0f * 16 * Kp) / (4194304.0f * 0.032768f * 16);// 单位：kWh/pulse
#elif defined(BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER)
    // 电压分压电阻采样方式参数
    static constexpr float Rf = 1500.0f;        // 分压上拉电阻 (kΩ)
    static constexpr float Rv = 1.0f;            // 分压下拉电阻 (kΩ)
    
    // 校准系数计算
    static constexpr float Ki = 12875.0f * Gain_I * 2 * RL * 1000 / Rt / Vref;
    static constexpr float Kv = 13162.0f * Rv * 1000 * Gain_V / (Vref * (Rf + Rv));
    //static constexpr float Kp = 2.3825e-8 * Kv * Ki;
    static constexpr float Kp = (40.4125 * Gain_V * Gain_I * RL * 2 * 1000 / Rt) * Rv * 1000/(Vref * Vref * (Rf + Rv));
    static constexpr float Ke = (3600000.0f * 16 * Kp) / (4194304.0f * 0.032768f * 16);// 单位：kWh/pulse
    
#else
    #error "必须定义 BL0906_VOLTAGE_SAMPLING_TRANSFORMER 或 BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER"
#endif

// 通用校准系数（两种模式都使用）
static constexpr float Kp_sum = Kp / 16;
static constexpr float Ke_sum = Ke / 16;
static constexpr float FREF = 1 / 10000000.0f;     // 频率转换
static constexpr float TREF = 59 - 40 / 12.5f;     // 温度转换

}  // namespace bl0906_factory
}  // namespace esphome



