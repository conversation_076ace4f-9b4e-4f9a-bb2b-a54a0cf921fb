packages: 
  bl0906: !include packages/bl0906_factory_6ch_calib.yaml

substitutions:
  name: "6-ch-monitor-20-calib"
  friendly_name: "6-ch-monitor-20-calib"
  ch1: "ch_1"   #可根据需要修改名称
  ch2: "ch_2"   #可根据需要修改名称
  ch3: "ch_3"    #可根据需要修改名称
  ch4: "ch_4"    #可根据需要修改名称
  ch5: "ch_5"  #可根据需要修改名称
  ch6: "ch_6"     #可根据需要修改名称
esphome:
  name: "${name}"
  name_add_mac_suffix: False
  friendly_name: "${friendly_name}"
  libraries:
    - EEPROM
  project:
    name: carrot8848.6-ch-monitor
    version: "2.0"
  platformio_options:
    # board_build.partitions: ./partitions.csv
    build_flags:
      - -DBL0906_CALIBRATION_MODE=1
      - -DUSE_EEPROM_CALIBRATION=1
esp32:
  board: esp32-c3-devkitm-1
  framework:
    type: arduino

preferences:
  flash_write_interval: 30min
# Enable logging
logger:
  level: INFO
  logs:
    sensor:094: none
    bl0906_factory: INFO
udp:
  listen_address: ************
  port: 1314
syslog:
# Enable Home Assistant API
api:
  reboot_timeout: 0s

ota:
  - platform: esphome
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  power_save_mode: none
  manual_ip:
    static_ip: ************
    gateway: *************
    subnet: *************
  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    # ssid: "${name}"
    password: ""

captive_portal:
web_server:
  port: 80
  local: True
  version: 3
  sorting_groups:
    - id: pzem_004T
      name: "PZEM-004T"
      sorting_weight: 10
    - id: current
      name: "Current"
      sorting_weight: 20
    - id: power
      name: "Power"
      sorting_weight: 30
    - id: energy
      name: "Energy"
      sorting_weight: 40
    - id: energy_stats
      name: "Energy Status"
      sorting_weight: 50
    - id: miscellaneous
      name: "Miscellaneous"
      sorting_weight: 60


status_led:
  pin: GPIO10
uart:
  # - id: uart_bus2
  #   rx_pin: 5
  #   tx_pin: 4
  #   baud_rate: 115200
  - id: uart_bus
    rx_pin: 6
    tx_pin: 7
    baud_rate: 19200
  - id: uart_bus3
    rx_pin: 20
    tx_pin: 21
    baud_rate: 115200

packet_transport:
  - platform: udp
    providers:
       - name: develop-platform-1
modbus:
  - uart_id: uart_bus2
    id: mod_bus1
sensor:
  - platform: packet_transport
    provider: develop-platform-1
    id: unit1_current
  - platform: packet_transport
    provider: develop-platform-1
    id: unit1_voltage
    
  # - platform: pzemac
  #   id: sensor_pzem
  #   update_interval: 60s
  #   current:
  #     name: "PZEM Current"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   voltage:
  #     name: "PZEM Voltage"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   energy:
  #     name: "PZEM Energy"
  #     id: pzem_energy
  #     filters:
  #       # Multiplication factor from Wh to kWh is 0.001
  #       - multiply: 0.001
  #     unit_of_measurement: kWh
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   power:
  #     name: "PZEM Power"
  #     id: pzemac_power
  #     filters:
  #       # Multiplication factor from W to kW is 0.001
  #      - multiply: 0.001
  #     unit_of_measurement: kW
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   frequency:
  #     name: "PZEM Frequency"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   power_factor:
  #     name: "PZEM Power Factor"
  #     web_server:
  #         sorting_group_id: pzem_004T
  #   modbus_id: mod_bus1
  # - platform: "energy_statistics"
  #   total: energy
  #   energy_today:
  #     name: "PZEM Energy Today"
  #   energy_yesterday:
  #     name: "PZEM Energy Yesterday"
  #   energy_week:
  #     name: "PZEM Energy Week"
  #   energy_month:
  #     name: "PZEM Energy Month"

  - platform: wifi_signal # Reports the WiFi signal strength/RSSI in dB
    name: "Energy_meter WiFi Signal dB"
    id: wifi_signal_db
    update_interval: 60s
    entity_category: "diagnostic"

  - platform: copy # Reports the WiFi signal strength in %
    source_id: wifi_signal_db
    name: "Energy_meter WiFi Signal Percent"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    entity_category: "diagnostic"
switch:
  - platform: restart
    name: "${name} controller Restart"
  - platform: factory_reset
    name: Restart with Factory Default Settings
# interval:
#   - interval: 10s
#     id: read_sensors
#     then: 
#       - component.update: sensor_bl0906
#       - delay: 0.5s
#       - component.update: sensor_pzem

time:
  - platform: sntp
    id: ha_time
    servers: ntp.aliyun.com
text_sensor:
  - platform: wifi_info
    ip_address:
      name: Energy_meter IP Address
    ssid:
      name: Energy_meter Connected SSID
    bssid:
      name: Energy_meter Connected BSSID
    mac_address:
      name: Energy_meter Mac Wifi Address
    # scan_results:
    #   name: Energy_meter Latest Scan Results
    dns_address:
      name: Energy_meter DNS Address 
    