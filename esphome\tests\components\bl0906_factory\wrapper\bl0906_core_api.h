#pragma once

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// BL0906 Core API - 预编译库接口
// 这个文件定义了薄包装层与预编译库之间的接口

// 结果状态枚举
typedef enum {
    BL0906_SUCCESS = 0,
    BL0906_ERROR_INVALID_PARAM = 1,
    BL0906_ERROR_COMMUNICATION = 2,
    BL0906_ERROR_TIMEOUT = 3,
    BL0906_ERROR_CHECKSUM = 4,
    BL0906_ERROR_WRITE_PROTECT = 5,
    BL0906_ERROR_UNKNOWN = 99
} bl0906_result_t;

// 芯片型号枚举
typedef enum {
    BL0906_CHIP_BL0906 = 0,
    BL0906_CHIP_BL0910 = 1
} bl0906_chip_model_t;

// 通信方式枚举
typedef enum {
    BL0906_COMM_UART = 0,
    BL0906_COMM_SPI = 1
} bl0906_comm_type_t;

// 寄存器类型枚举（从 bl0906_chip_params.h 提取）
typedef enum {
    BL0906_REG_I_RMS = 0,     // 电流有效值
    BL0906_REG_WATT = 1,      // 功率
    BL0906_REG_CF_CNT = 2,    // 脉冲计数
    BL0906_REG_RMSGN = 3,     // 有效值增益
    BL0906_REG_RMSOS = 4,     // 有效值偏置
    BL0906_REG_CHGN = 5,      // 电流增益
    BL0906_REG_CHOS = 6,      // 电流偏置
    BL0906_REG_WATTGN = 7,    // 功率增益
    BL0906_REG_WATTOS = 8,    // 功率偏置
    BL0906_REG_CHGN_V = 9,    // 电压增益
    BL0906_REG_CHOS_V = 10,   // 电压偏置
    BL0906_REG_VOLTAGE = 11,  // 电压
    BL0906_REG_FREQUENCY = 12,// 频率
    BL0906_REG_TEMPERATURE = 13,// 温度
    BL0906_REG_WATT_SUM = 14, // 总功率
    BL0906_REG_CF_SUM = 15,   // 总脉冲计数
    BL0906_REG_MODE2 = 16     // 工作模式寄存器2
} bl0906_register_type_t;

// 电压采样模式枚举（从 bl0906_calibration.h 提取）
typedef enum {
    BL0906_VOLTAGE_SAMPLING_TRANSFORMER = 0,    // 电压互感器采样
    BL0906_VOLTAGE_SAMPLING_RESISTOR_DIVIDER = 1 // 电阻分压采样
} bl0906_voltage_sampling_mode_t;

// 芯片信息结构
typedef struct {
    uint8_t max_channels;
    const char* chip_name;
    bool is_valid;
} bl0906_chip_info_t;

// 校准系数结构（从 bl0906_calibration.h 提取）
typedef struct {
    float Ki;      // 电流系数
    float Kv;      // 电压系数
    float Kp;      // 功率系数
    float Ke;      // 电量系数
    float Kp_sum;  // 总功率系数
    float Ke_sum;  // 总电量系数
    float FREF;    // 频率转换系数
    float TREF;    // 温度转换系数
} bl0906_calibration_coefficients_t;

// 参考参数结构（从 bl0906_calibration.h 提取）
typedef struct {
    float Vref;    // 内部参考电压 (V)
    int Gain_V;    // 电压通道增益
    int Gain_I;    // 电流通道增益
    float RL;      // 互感器副边负载电阻 (Ω)
    float Rt;      // 互感器变比
    float Rf;      // 分压电阻 (Ω)
    float R46;     // 电压采样电阻 (Ω) - 仅互感器模式
    float Rv;      // 分压下拉电阻 (kΩ) - 仅电阻分压模式
} bl0906_reference_params_t;

// 传感器数据结构
typedef struct {
    float voltage;
    float current;
    float power;
    float energy;
    float frequency;
    float temperature;
    bool valid;
} bl0906_sensor_data_t;

// 校准数据结构
typedef struct {
    uint8_t register_addr;
    int16_t value;
} bl0906_calibration_entry_t;

// 通信回调函数类型
typedef bl0906_result_t (*bl0906_read_register_func_t)(uint8_t address, int32_t* value);
typedef bl0906_result_t (*bl0906_write_register_func_t)(uint8_t address, int16_t value);
typedef bl0906_result_t (*bl0906_send_raw_command_func_t)(const uint8_t* data, size_t length);

// 通信回调结构
typedef struct {
    bl0906_read_register_func_t read_register;
    bl0906_write_register_func_t write_register;
    bl0906_send_raw_command_func_t send_raw_command;
} bl0906_comm_callbacks_t;

// ============================================================================
// 核心API函数声明
// ============================================================================

/**
 * @brief 初始化BL0906核心库
 * @param chip_model 芯片型号
 * @param comm_type 通信方式
 * @param callbacks 通信回调函数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_init(bl0906_chip_model_t chip_model, 
                                bl0906_comm_type_t comm_type,
                                const bl0906_comm_callbacks_t* callbacks);

/**
 * @brief 读取传感器数据
 * @param channel 通道号（0为总通道）
 * @param data 输出的传感器数据
 * @return 操作结果
 */
bl0906_result_t bl0906_core_read_sensor_data(uint8_t channel, bl0906_sensor_data_t* data);

/**
 * @brief 转换原始寄存器值为实际值
 * @param register_addr 寄存器地址
 * @param raw_value 原始值
 * @param converted_value 转换后的值
 * @return 操作结果
 */
bl0906_result_t bl0906_core_convert_raw_to_value(uint8_t register_addr, 
                                                int32_t raw_value, 
                                                float* converted_value);

/**
 * @brief 应用校准数据到芯片
 * @param entries 校准条目数组
 * @param count 校准条目数量
 * @return 操作结果
 */
bl0906_result_t bl0906_core_apply_calibration(const bl0906_calibration_entry_t* entries, 
                                             size_t count);

/**
 * @brief 计算校准系数
 * @param register_addr 寄存器地址
 * @param raw_value 原始测量值
 * @param reference_value 参考值
 * @param calibration_coefficient 计算出的校准系数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_calculate_calibration(uint8_t register_addr,
                                                 int32_t raw_value,
                                                 float reference_value,
                                                 int16_t* calibration_coefficient);

/**
 * @brief 解除写保护
 * @return 操作结果
 */
bl0906_result_t bl0906_core_disable_write_protection(void);

/**
 * @brief 设置电网频率模式
 * @param is_60hz true为60Hz，false为50Hz
 * @return 操作结果
 */
bl0906_result_t bl0906_core_set_frequency_mode(bool is_60hz);

/**
 * @brief 检测电网频率
 * @param frequency 检测到的频率值
 * @return 操作结果
 */
bl0906_result_t bl0906_core_detect_grid_frequency(float* frequency);

// ============================================================================
// 芯片参数相关API（从 bl0906_chip_params.h 迁移）
// ============================================================================

/**
 * @brief 获取芯片信息
 * @param chip_model 芯片型号
 * @param info 输出的芯片信息
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_chip_info(bl0906_chip_model_t chip_model, 
                                          bl0906_chip_info_t* info);

/**
 * @brief 获取寄存器地址
 * @param chip_model 芯片型号
 * @param reg_type 寄存器类型
 * @param channel 通道号（-1表示电压通道）
 * @param address 输出的寄存器地址
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_register_address(bl0906_chip_model_t chip_model,
                                                 bl0906_register_type_t reg_type,
                                                 int channel,
                                                 uint8_t* address);

/**
 * @brief 验证寄存器地址是否有效
 * @param chip_model 芯片型号
 * @param address 寄存器地址
 * @param is_valid 输出是否有效
 * @return 操作结果
 */
bl0906_result_t bl0906_core_validate_register_address(bl0906_chip_model_t chip_model,
                                                      uint8_t address,
                                                      bool* is_valid);

/**
 * @brief 检查寄存器是否为16位寄存器
 * @param address 寄存器地址
 * @param is_16bit 输出是否为16位
 * @return 操作结果
 */
bl0906_result_t bl0906_core_is_16bit_register(uint8_t address, bool* is_16bit);

/**
 * @brief 检查寄存器是否为无符号寄存器
 * @param address 寄存器地址
 * @param is_unsigned 输出是否为无符号
 * @return 操作结果
 */
bl0906_result_t bl0906_core_is_unsigned_register(uint8_t address, bool* is_unsigned);

// ============================================================================
// 校准系数相关API（从 bl0906_calibration.h 迁移）
// ============================================================================

/**
 * @brief 计算校准系数
 * @param sampling_mode 电压采样模式
 * @param ref_params 参考参数
 * @param coefficients 输出的校准系数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_calculate_calibration_coefficients(bl0906_voltage_sampling_mode_t sampling_mode,
                                                              const bl0906_reference_params_t* ref_params,
                                                              bl0906_calibration_coefficients_t* coefficients);

/**
 * @brief 获取默认参考参数
 * @param sampling_mode 电压采样模式
 * @param ref_params 输出的默认参考参数
 * @return 操作结果
 */
bl0906_result_t bl0906_core_get_default_reference_params(bl0906_voltage_sampling_mode_t sampling_mode,
                                                         bl0906_reference_params_t* ref_params);

// ============================================================================
// 工具函数
// ============================================================================

/**
 * @brief 获取错误描述
 * @param result 错误代码
 * @return 错误描述字符串
 */
const char* bl0906_core_get_error_string(bl0906_result_t result);

/**
 * @brief 获取库版本信息
 * @return 版本字符串
 */
const char* bl0906_core_get_version(void);

/**
 * @brief 反初始化核心库
 */
void bl0906_core_deinit(void);

#ifdef __cplusplus
}
#endif 